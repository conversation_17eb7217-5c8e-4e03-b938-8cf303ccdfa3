redis_cache: redis-server config/redis_cache.conf
redis_queue: redis-server config/redis_queue.conf


web: /Library/Frameworks/Python.framework/Versions/3.10/bin/bench serve  --port 8000


socketio: /usr/local/bin/node apps/frappe/socketio.js


watch: /Library/Frameworks/Python.framework/Versions/3.10/bin/bench watch


schedule: /Library/Frameworks/Python.framework/Versions/3.10/bin/bench schedule

worker: OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES NO_PROXY=* /Library/Frameworks/Python.framework/Versions/3.10/bin/bench worker 1>> logs/worker.log 2>> logs/worker.error.log

