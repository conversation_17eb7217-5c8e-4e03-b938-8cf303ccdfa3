# FC-CHINA Manufacturing Extensions

## Overview

This document outlines how to extend ERPNext's manufacturing module to provide production transparency and real-time tracking for customers. The extensions maintain all existing manufacturing functionality while adding customer-visible features for order tracking and production monitoring.

## Manufacturing Module Analysis

### Current ERPNext Manufacturing Components
ERPNext's manufacturing module includes these core DocTypes:
- **BOM (Bill of Materials)**: Product structure and material requirements
- **Work Order**: Production orders with operations and materials
- **Job Card**: Individual operation tracking and time recording
- **Production Plan**: Multi-item production planning
- **Routing**: Operation sequences and workstation assignments
- **Workstation**: Manufacturing resources and capacity
- **Operation**: Standard manufacturing operations

### Extension Strategy
- **Preserve Core Functionality**: All existing manufacturing features remain intact
- **Add Customer Visibility**: Extend existing DocTypes with customer-facing fields
- **Real-time Updates**: Implement automatic customer notifications
- **Photo Documentation**: Add visual progress tracking
- **Milestone Tracking**: Create customer-friendly progress indicators

## Work Order Extensions

### Enhanced Work Order DocType
```python
# fc_china/manufacturing_extensions/doctype/work_order_extension/work_order_extension.py
class WorkOrderExtension:
    def extend_work_order(self):
        """Add customer visibility fields to Work Order"""
        
        # Add custom fields to Work Order
        custom_fields = [
            {
                'fieldname': 'customer_visible',
                'label': 'Customer Visible',
                'fieldtype': 'Check',
                'default': 1,
                'description': 'Allow customer to track this work order'
            },
            {
                'fieldname': 'customer_reference',
                'label': 'Customer Reference',
                'fieldtype': 'Data',
                'description': 'Customer order reference number'
            },
            {
                'fieldname': 'estimated_delivery_date',
                'label': 'Estimated Delivery Date',
                'fieldtype': 'Date',
                'description': 'Promised delivery date to customer'
            },
            {
                'fieldname': 'production_milestones',
                'label': 'Production Milestones',
                'fieldtype': 'Table',
                'options': 'Production Milestone'
            },
            {
                'fieldname': 'customer_notes',
                'label': 'Customer Notes',
                'fieldtype': 'Text Editor',
                'description': 'Notes visible to customer'
            }
        ]
        
        for field in custom_fields:
            self.add_custom_field('Work Order', field)

# Extend Work Order class
class WorkOrder(Document):
    def on_update(self):
        """Override to add customer notifications"""
        super().on_update()
        
        if self.customer_visible and self.has_value_changed('status'):
            self.notify_customer_status_change()
    
    def notify_customer_status_change(self):
        """Send real-time notification to customer"""
        if hasattr(self, 'sales_order') and self.sales_order:
            sales_order = frappe.get_doc('Sales Order', self.sales_order)
            customer = sales_order.customer
            
            # Send WebSocket notification
            notification_data = {
                'type': 'production_update',
                'work_order': self.name,
                'status': self.status,
                'progress_percentage': self.get_progress_percentage(),
                'estimated_delivery': self.estimated_delivery_date,
                'timestamp': frappe.utils.now()
            }
            
            self.send_customer_notification(customer, notification_data)
    
    def get_progress_percentage(self):
        """Calculate production progress percentage"""
        if self.status == 'Draft':
            return 0
        elif self.status == 'Not Started':
            return 5
        elif self.status == 'In Process':
            # Calculate based on completed operations
            total_operations = len(self.operations)
            completed_operations = sum(1 for op in self.operations if op.status == 'Completed')
            return min(95, 10 + (completed_operations / total_operations * 80))
        elif self.status == 'Completed':
            return 100
        else:
            return 0
```

### Production Milestone Tracking
```python
# fc_china/manufacturing_extensions/doctype/production_milestone/production_milestone.py
class ProductionMilestone(Document):
    def validate(self):
        self.validate_work_order_exists()
        self.validate_milestone_sequence()
    
    def on_update(self):
        if self.status == 'Completed' and not self.completion_date:
            self.completion_date = frappe.utils.now()
            
        # Notify customer of milestone completion
        if self.customer_visible and self.has_value_changed('status'):
            self.notify_milestone_completion()
    
    def notify_milestone_completion(self):
        """Send milestone completion notification to customer"""
        work_order = frappe.get_doc('Work Order', self.work_order)
        
        if work_order.sales_order:
            sales_order = frappe.get_doc('Sales Order', work_order.sales_order)
            
            notification_data = {
                'type': 'milestone_completed',
                'work_order': self.work_order,
                'milestone_name': self.milestone_name,
                'completion_date': self.completion_date,
                'photos': self.get_milestone_photos(),
                'next_milestone': self.get_next_milestone()
            }
            
            self.send_customer_notification(sales_order.customer, notification_data)

# Milestone schema
milestone_fields = [
    {
        'fieldname': 'milestone_name',
        'label': 'Milestone Name',
        'fieldtype': 'Data',
        'reqd': 1
    },
    {
        'fieldname': 'description',
        'label': 'Description',
        'fieldtype': 'Text Editor'
    },
    {
        'fieldname': 'target_date',
        'label': 'Target Date',
        'fieldtype': 'Date'
    },
    {
        'fieldname': 'completion_date',
        'label': 'Completion Date',
        'fieldtype': 'Datetime',
        'read_only': 1
    },
    {
        'fieldname': 'status',
        'label': 'Status',
        'fieldtype': 'Select',
        'options': 'Pending\nIn Progress\nCompleted\nDelayed',
        'default': 'Pending'
    },
    {
        'fieldname': 'customer_visible',
        'label': 'Customer Visible',
        'fieldtype': 'Check',
        'default': 1
    },
    {
        'fieldname': 'photos',
        'label': 'Photos',
        'fieldtype': 'Table',
        'options': 'Milestone Photo'
    },
    {
        'fieldname': 'notes',
        'label': 'Notes',
        'fieldtype': 'Text Editor'
    }
]
```

## Job Card Extensions

### Enhanced Job Card for Customer Visibility
```python
# Extend Job Card with customer-facing features
class JobCardExtension:
    def extend_job_card(self):
        """Add customer visibility to Job Card operations"""
        
        custom_fields = [
            {
                'fieldname': 'customer_visible',
                'label': 'Customer Visible',
                'fieldtype': 'Check',
                'default': 0,
                'description': 'Show this operation to customer'
            },
            {
                'fieldname': 'operation_photos',
                'label': 'Operation Photos',
                'fieldtype': 'Table',
                'options': 'Operation Photo'
            },
            {
                'fieldname': 'quality_checkpoints',
                'label': 'Quality Checkpoints',
                'fieldtype': 'Table',
                'options': 'Quality Checkpoint'
            },
            {
                'fieldname': 'customer_notes',
                'label': 'Customer Notes',
                'fieldtype': 'Text Editor',
                'description': 'Notes visible to customer about this operation'
            }
        ]
        
        for field in custom_fields:
            self.add_custom_field('Job Card', field)

class JobCard(Document):
    def on_update(self):
        super().on_update()
        
        if self.customer_visible and self.has_value_changed('status'):
            self.update_work_order_progress()
            self.notify_customer_operation_update()
    
    def notify_customer_operation_update(self):
        """Notify customer of operation progress"""
        work_order = frappe.get_doc('Work Order', self.work_order)
        
        if work_order.customer_visible and work_order.sales_order:
            sales_order = frappe.get_doc('Sales Order', work_order.sales_order)
            
            notification_data = {
                'type': 'operation_update',
                'work_order': self.work_order,
                'operation': self.operation,
                'status': self.status,
                'completion_percentage': self.get_completion_percentage(),
                'photos': self.get_operation_photos(),
                'timestamp': frappe.utils.now()
            }
            
            self.send_customer_notification(sales_order.customer, notification_data)
```

## Quality Management Integration

### Quality Checkpoint System
```python
# fc_china/manufacturing_extensions/doctype/quality_checkpoint/quality_checkpoint.py
class QualityCheckpoint(Document):
    def validate(self):
        self.validate_checkpoint_criteria()
    
    def on_update(self):
        if self.status == 'Passed' and self.customer_visible:
            self.notify_customer_quality_update()
    
    def notify_customer_quality_update(self):
        """Notify customer of quality checkpoint completion"""
        notification_data = {
            'type': 'quality_checkpoint',
            'checkpoint_name': self.checkpoint_name,
            'status': self.status,
            'inspector': self.inspector,
            'inspection_date': self.inspection_date,
            'report_url': self.get_report_url(),
            'photos': self.get_checkpoint_photos()
        }
        
        self.send_customer_notification(self.get_customer(), notification_data)

# Quality checkpoint fields
quality_checkpoint_fields = [
    {
        'fieldname': 'checkpoint_name',
        'label': 'Checkpoint Name',
        'fieldtype': 'Data',
        'reqd': 1
    },
    {
        'fieldname': 'work_order',
        'label': 'Work Order',
        'fieldtype': 'Link',
        'options': 'Work Order'
    },
    {
        'fieldname': 'job_card',
        'label': 'Job Card',
        'fieldtype': 'Link',
        'options': 'Job Card'
    },
    {
        'fieldname': 'inspection_criteria',
        'label': 'Inspection Criteria',
        'fieldtype': 'Table',
        'options': 'Inspection Criteria'
    },
    {
        'fieldname': 'status',
        'label': 'Status',
        'fieldtype': 'Select',
        'options': 'Pending\nIn Progress\nPassed\nFailed\nRework Required'
    },
    {
        'fieldname': 'inspector',
        'label': 'Inspector',
        'fieldtype': 'Link',
        'options': 'User'
    },
    {
        'fieldname': 'inspection_date',
        'label': 'Inspection Date',
        'fieldtype': 'Datetime'
    },
    {
        'fieldname': 'customer_visible',
        'label': 'Customer Visible',
        'fieldtype': 'Check',
        'default': 1
    },
    {
        'fieldname': 'report_attachment',
        'label': 'Quality Report',
        'fieldtype': 'Attach'
    }
]
```

## Photo Documentation System

### Production Photo Management
```python
# fc_china/manufacturing_extensions/doctype/production_photo/production_photo.py
class ProductionPhoto(Document):
    def validate(self):
        self.validate_file_type()
        self.validate_file_size()
    
    def on_insert(self):
        self.process_image()
        self.notify_customer_photo_added()
    
    def process_image(self):
        """Process uploaded image for customer viewing"""
        # Resize image for web/mobile viewing
        # Add watermark if required
        # Generate thumbnail
        pass
    
    def notify_customer_photo_added(self):
        """Notify customer of new production photo"""
        if self.customer_visible:
            notification_data = {
                'type': 'production_photo',
                'work_order': self.work_order,
                'milestone': self.milestone,
                'photo_url': self.get_photo_url(),
                'caption': self.caption,
                'timestamp': self.creation
            }
            
            self.send_customer_notification(self.get_customer(), notification_data)

# Photo documentation fields
photo_fields = [
    {
        'fieldname': 'work_order',
        'label': 'Work Order',
        'fieldtype': 'Link',
        'options': 'Work Order'
    },
    {
        'fieldname': 'milestone',
        'label': 'Milestone',
        'fieldtype': 'Link',
        'options': 'Production Milestone'
    },
    {
        'fieldname': 'job_card',
        'label': 'Job Card',
        'fieldtype': 'Link',
        'options': 'Job Card'
    },
    {
        'fieldname': 'photo_file',
        'label': 'Photo',
        'fieldtype': 'Attach Image',
        'reqd': 1
    },
    {
        'fieldname': 'caption',
        'label': 'Caption',
        'fieldtype': 'Data'
    },
    {
        'fieldname': 'photo_type',
        'label': 'Photo Type',
        'fieldtype': 'Select',
        'options': 'Setup\nIn Progress\nQuality Check\nCompleted\nIssue\nRework'
    },
    {
        'fieldname': 'customer_visible',
        'label': 'Customer Visible',
        'fieldtype': 'Check',
        'default': 1
    },
    {
        'fieldname': 'taken_by',
        'label': 'Taken By',
        'fieldtype': 'Link',
        'options': 'User'
    }
]
```

## Customer Dashboard Integration

### Production Tracking Dashboard
```python
# fc_china/manufacturing_extensions/dashboard.py
class ProductionTrackingDashboard:
    def get_customer_production_data(self, customer_id, tenant_id):
        """Get production tracking data for customer dashboard"""
        
        tenant = self.get_tenant(tenant_id)
        
        # Get active work orders for customer
        work_orders = frappe.get_all('Work Order',
            filters={
                'company': tenant.company,
                'customer_visible': 1,
                'status': ['in', ['Not Started', 'In Process']]
            },
            fields=['name', 'item_code', 'qty', 'status', 'planned_start_date', 
                   'planned_end_date', 'estimated_delivery_date']
        )
        
        # Enrich with progress data
        for wo in work_orders:
            wo.update({
                'progress_percentage': self.get_work_order_progress(wo.name),
                'current_milestone': self.get_current_milestone(wo.name),
                'recent_photos': self.get_recent_photos(wo.name),
                'quality_status': self.get_quality_status(wo.name)
            })
        
        return {
            'active_work_orders': work_orders,
            'production_summary': self.get_production_summary(customer_id, tenant.company),
            'recent_updates': self.get_recent_updates(customer_id, tenant.company)
        }
    
    def get_work_order_progress(self, work_order):
        """Calculate detailed work order progress"""
        wo = frappe.get_doc('Work Order', work_order)
        
        if wo.status == 'Completed':
            return 100
        
        # Calculate based on milestones and operations
        milestone_progress = self.get_milestone_progress(work_order)
        operation_progress = self.get_operation_progress(work_order)
        
        # Weighted average (milestones 60%, operations 40%)
        return (milestone_progress * 0.6) + (operation_progress * 0.4)
```

## Real-time Notification System

### WebSocket Integration for Manufacturing Updates
```python
# fc_china/manufacturing_extensions/notifications.py
class ManufacturingNotifications:
    def __init__(self):
        self.websocket_manager = WebSocketManager()
    
    def send_production_update(self, customer_id, tenant_id, update_data):
        """Send real-time production update to customer"""
        
        # Format notification for customer
        notification = {
            'type': 'production_update',
            'timestamp': frappe.utils.now(),
            'data': update_data
        }
        
        # Send via WebSocket
        self.websocket_manager.send_to_customer(
            tenant_id, customer_id, notification
        )
        
        # Also send push notification if customer is offline
        self.send_push_notification(customer_id, notification)
    
    def send_milestone_notification(self, customer_id, tenant_id, milestone_data):
        """Send milestone completion notification"""
        
        notification = {
            'type': 'milestone_completed',
            'title': f"Milestone Completed: {milestone_data['milestone_name']}",
            'message': f"Your order {milestone_data['work_order']} has reached a new milestone",
            'data': milestone_data
        }
        
        self.websocket_manager.send_to_customer(
            tenant_id, customer_id, notification
        )
        
        # Send email notification
        self.send_email_notification(customer_id, notification)
```

## API Endpoints for Manufacturing Data

### Customer-Facing Manufacturing APIs
```python
# fc_china/manufacturing_extensions/api.py
@frappe.whitelist()
def get_order_production_status(order_id):
    """Get production status for customer order"""
    
    # Validate customer access to order
    if not validate_customer_order_access(order_id):
        frappe.throw("Access denied", frappe.PermissionError)
    
    sales_order = frappe.get_doc('Sales Order', order_id)
    
    # Get associated work orders
    work_orders = frappe.get_all('Work Order',
        filters={'sales_order': order_id, 'customer_visible': 1},
        fields=['name', 'item_code', 'qty', 'status', 'planned_start_date', 
               'planned_end_date', 'estimated_delivery_date']
    )
    
    production_data = []
    for wo in work_orders:
        wo_doc = frappe.get_doc('Work Order', wo.name)
        
        production_data.append({
            'work_order': wo.name,
            'item': wo.item_code,
            'quantity': wo.qty,
            'status': wo.status,
            'progress_percentage': wo_doc.get_progress_percentage(),
            'milestones': get_work_order_milestones(wo.name),
            'photos': get_work_order_photos(wo.name),
            'quality_checkpoints': get_quality_checkpoints(wo.name),
            'estimated_delivery': wo.estimated_delivery_date
        })
    
    return {
        'order_id': order_id,
        'production_items': production_data,
        'overall_progress': calculate_overall_progress(work_orders),
        'estimated_completion': get_latest_delivery_date(work_orders)
    }

@frappe.whitelist()
def get_production_photos(work_order):
    """Get production photos for work order"""
    
    if not validate_customer_work_order_access(work_order):
        frappe.throw("Access denied", frappe.PermissionError)
    
    photos = frappe.get_all('Production Photo',
        filters={'work_order': work_order, 'customer_visible': 1},
        fields=['photo_file', 'caption', 'photo_type', 'creation', 'taken_by'],
        order_by='creation desc'
    )
    
    return photos
```

## Integration with Existing ERPNext Modules

### Sales Order Integration
```python
# Extend Sales Order to link with production tracking
class SalesOrderExtension:
    def extend_sales_order(self):
        """Add production tracking fields to Sales Order"""
        
        custom_fields = [
            {
                'fieldname': 'production_tracking_enabled',
                'label': 'Enable Production Tracking',
                'fieldtype': 'Check',
                'default': 1
            },
            {
                'fieldname': 'customer_portal_access',
                'label': 'Customer Portal Access',
                'fieldtype': 'Check',
                'default': 1
            },
            {
                'fieldname': 'production_milestones_template',
                'label': 'Production Milestones Template',
                'fieldtype': 'Link',
                'options': 'Milestone Template'
            }
        ]
        
        for field in custom_fields:
            self.add_custom_field('Sales Order', field)
```

### Stock Entry Integration
```python
# Link stock entries with customer notifications
class StockEntryExtension:
    def on_submit(self):
        """Notify customer of material consumption/completion"""
        
        if self.purpose == 'Material Transfer for Manufacture':
            self.notify_production_start()
        elif self.purpose == 'Manufacture':
            self.notify_production_completion()
    
    def notify_production_start(self):
        """Notify customer that production has started"""
        if self.work_order:
            work_order = frappe.get_doc('Work Order', self.work_order)
            if work_order.customer_visible:
                # Send notification
                pass
```

## Cross-References

- [Technical Architecture](./TECHNICAL-ARCHITECTURE.md) - System architecture overview
- [Multi-Tenant Design](./MULTI-TENANT-DESIGN.md) - Tenant isolation for manufacturing data
- [API Specifications](./API-SPECIFICATIONS.md) - Manufacturing API endpoints
- [Customer Integration](./CUSTOMER-INTEGRATION.md) - Customer-facing features
- [Development Standards](./DEVELOPMENT-STANDARDS.md) - Development guidelines
