# FC-CHINA Customer Integration

## Overview

The customer integration layer transforms ERPNext from a factory-internal system into a customer-facing B2B marketplace designed for Chinese factories. This document outlines how customers interact with Chinese factories through web and mobile applications with Chinese language priority while maintaining secure access to production data and communication channels. **IMPORTANT: FC-CHINA does NOT process payments - only displays payment references and status.**

## Customer User Journey

### 1. Discovery & Registration
```mermaid
graph TD
    A[Customer discovers factory] --> B[Request access/invitation]
    B --> C[Factory admin approves]
    C --> D[Customer receives invitation]
    D --> E[Customer registers account]
    E --> F[Account verification]
    F --> G[Access granted to factory portal]
```

### 2. Product Browsing & Ordering
```mermaid
graph TD
    A[Browse product catalog] --> B[View product details]
    B --> C[Request quote/samples]
    C --> D[Negotiate terms]
    D --> E[Place order]
    E --> F[Order confirmation]
    F --> G[Production tracking begins]
```

## Customer Authentication System

### Registration Process
```python
# fc_china/customer_portal/registration.py
class CustomerRegistration:
    def register_customer(self, registration_data, invitation_code):
        """Register new customer with factory access"""
        
        # Validate invitation code
        invitation = self.validate_invitation(invitation_code)
        if not invitation:
            raise ValidationError("Invalid invitation code")
        
        # Create customer record
        customer = frappe.get_doc({
            'doctype': 'Customer',
            'customer_name': registration_data['name'],
            'customer_type': 'Company',
            'customer_group': 'B2B Customers',
            'territory': registration_data.get('territory', 'All Territories'),
            'company': invitation.factory_company,
            'email_id': registration_data['email'],
            'mobile_no': registration_data.get('phone'),
            'website': registration_data.get('website')
        })
        customer.insert()
        
        # Create user account
        user = frappe.get_doc({
            'doctype': 'User',
            'email': registration_data['email'],
            'first_name': registration_data['first_name'],
            'last_name': registration_data['last_name'],
            'user_type': 'Website User',
            'role_profile_name': 'Factory Customer'
        })
        user.insert()
        
        # Create customer-factory access record
        access = frappe.get_doc({
            'doctype': 'Customer Factory Access',
            'customer': customer.name,
            'factory_company': invitation.factory_company,
            'tenant_id': invitation.tenant_id,
            'access_level': invitation.access_level,
            'permissions': invitation.permissions,
            'granted_date': frappe.utils.now()
        })
        access.insert()
        
        # Send welcome email
        self.send_welcome_email(user, customer, invitation.factory_name)
        
        return {
            'customer_id': customer.name,
            'user_id': user.name,
            'factory_access': access.name
        }
```

### Multi-Factory Access Management
```python
class CustomerFactoryAccess:
    def grant_factory_access(self, customer_id, tenant_id, access_level='standard'):
        """Grant customer access to additional factory"""
        
        # Check if access already exists
        existing_access = frappe.db.exists('Customer Factory Access', {
            'customer': customer_id,
            'tenant_id': tenant_id
        })
        
        if existing_access:
            raise ValidationError("Customer already has access to this factory")
        
        # Get tenant details
        tenant = frappe.get_doc('FC Tenant', {'tenant_id': tenant_id})
        
        # Create access record
        access = frappe.get_doc({
            'doctype': 'Customer Factory Access',
            'customer': customer_id,
            'factory_company': tenant.company,
            'tenant_id': tenant_id,
            'access_level': access_level,
            'permissions': self.get_default_permissions(access_level),
            'granted_date': frappe.utils.now(),
            'status': 'active'
        })
        access.insert()
        
        return access
    
    def get_customer_factories(self, customer_id):
        """Get all factories customer has access to"""
        return frappe.get_all('Customer Factory Access', 
            filters={'customer': customer_id, 'status': 'active'},
            fields=['tenant_id', 'factory_company', 'access_level', 'granted_date']
        )
```

## Customer Portal Features

### Dashboard Components
```python
# fc_china/customer_portal/dashboard.py
class CustomerDashboard:
    def get_dashboard_data(self, customer_id, tenant_id):
        """Get customer dashboard data for specific factory"""
        
        return {
            'summary': self.get_order_summary(customer_id, tenant_id),
            'recent_orders': self.get_recent_orders(customer_id, tenant_id),
            'production_updates': self.get_production_updates(customer_id, tenant_id),
            'messages': self.get_recent_messages(customer_id, tenant_id),
            'notifications': self.get_notifications(customer_id, tenant_id)
        }
    
    def get_order_summary(self, customer_id, tenant_id):
        """Get order statistics summary"""
        tenant = self.get_tenant(tenant_id)
        
        return {
            'total_orders': frappe.db.count('Sales Order', {
                'customer': customer_id,
                'company': tenant.company
            }),
            'active_orders': frappe.db.count('Sales Order', {
                'customer': customer_id,
                'company': tenant.company,
                'status': ['in', ['Draft', 'To Deliver and Bill', 'To Bill', 'To Deliver']]
            }),
            'completed_orders': frappe.db.count('Sales Order', {
                'customer': customer_id,
                'company': tenant.company,
                'status': 'Completed'
            }),
            'total_value': frappe.db.sql("""
                SELECT SUM(grand_total) 
                FROM `tabSales Order` 
                WHERE customer = %s AND company = %s
            """, (customer_id, tenant.company))[0][0] or 0
        }
```

### Product Catalog Integration
```python
class CustomerProductCatalog:
    def get_customer_catalog(self, customer_id, tenant_id, filters=None):
        """Get product catalog visible to customer"""
        
        tenant = self.get_tenant(tenant_id)
        
        # Base query for items
        query_filters = {
            'company': tenant.company,
            'disabled': 0,
            'is_sales_item': 1,
            'customer_visible': 1  # Custom field for customer visibility
        }
        
        # Apply additional filters
        if filters:
            if filters.get('category'):
                query_filters['item_group'] = filters['category']
            if filters.get('search'):
                query_filters['item_name'] = ['like', f"%{filters['search']}%"]
        
        items = frappe.get_all('Item', 
            filters=query_filters,
            fields=['name', 'item_name', 'description', 'item_group', 
                   'image', 'standard_rate', 'stock_uom'],
            order_by='creation desc',
            limit_page_length=filters.get('limit', 20),
            limit_start=filters.get('offset', 0)
        )
        
        # Enrich with additional data
        for item in items:
            item.update({
                'images': self.get_item_images(item.name),
                'specifications': self.get_item_specifications(item.name),
                'pricing': self.get_customer_pricing(item.name, customer_id),
                'availability': self.get_item_availability(item.name, tenant.company)
            })
        
        return items
```

## Mobile Application Architecture

### Flutter App Structure
```dart
// lib/main.dart
class FCChinaApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'FC-CHINA',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: SplashScreen(),
      routes: {
        '/login': (context) => LoginScreen(),
        '/dashboard': (context) => DashboardScreen(),
        '/products': (context) => ProductCatalogScreen(),
        '/orders': (context) => OrderListScreen(),
        '/production': (context) => ProductionTrackingScreen(),
        '/messages': (context) => MessagesScreen(),
        '/profile': (context) => ProfileScreen(),
      },
    );
  }
}
```

### State Management (Redux Pattern)
```dart
// lib/redux/app_state.dart
class AppState {
  final AuthState auth;
  final ProductState products;
  final OrderState orders;
  final ProductionState production;
  final MessageState messages;
  final NotificationState notifications;
  
  AppState({
    required this.auth,
    required this.products,
    required this.orders,
    required this.production,
    required this.messages,
    required this.notifications,
  });
}

// lib/redux/auth/auth_state.dart
class AuthState {
  final bool isAuthenticated;
  final User? user;
  final String? accessToken;
  final List<FactoryAccess> factoryAccess;
  final String? selectedTenantId;
  
  AuthState({
    this.isAuthenticated = false,
    this.user,
    this.accessToken,
    this.factoryAccess = const [],
    this.selectedTenantId,
  });
}
```

### API Service Layer
```dart
// lib/services/api_service.dart
class ApiService {
  static const String baseUrl = 'https://api.fc-china.com/v1';
  final Dio _dio;
  
  ApiService() : _dio = Dio() {
    _dio.interceptors.add(AuthInterceptor());
    _dio.interceptors.add(TenantInterceptor());
    _dio.interceptors.add(LoggingInterceptor());
  }
  
  Future<ApiResponse<List<Product>>> getProducts({
    String? category,
    String? search,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _dio.get('/products', queryParameters: {
        'category': category,
        'search': search,
        'page': page,
        'limit': limit,
      });
      
      return ApiResponse.success(
        (response.data['data']['products'] as List)
            .map((json) => Product.fromJson(json))
            .toList()
      );
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
}
```

### Offline Capability
```dart
// lib/services/offline_service.dart
class OfflineService {
  static const String dbName = 'fc_china.db';
  late Database _database;
  
  Future<void> initDatabase() async {
    _database = await openDatabase(
      dbName,
      version: 1,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE products (
            id TEXT PRIMARY KEY,
            name TEXT,
            description TEXT,
            category TEXT,
            price REAL,
            images TEXT,
            specifications TEXT,
            last_updated INTEGER
          )
        ''');
        
        await db.execute('''
          CREATE TABLE orders (
            id TEXT PRIMARY KEY,
            status TEXT,
            total_amount REAL,
            order_date INTEGER,
            items TEXT,
            last_updated INTEGER
          )
        ''');
      },
    );
  }
  
  Future<void> syncProductsOffline(List<Product> products) async {
    final batch = _database.batch();
    
    for (final product in products) {
      batch.insert(
        'products',
        product.toJson()..['last_updated'] = DateTime.now().millisecondsSinceEpoch,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    
    await batch.commit();
  }
}
```

## Real-time Communication

### WebSocket Integration
```dart
// lib/services/websocket_service.dart
class WebSocketService {
  IOWebSocketChannel? _channel;
  StreamController<WebSocketMessage> _messageController = StreamController.broadcast();
  
  Stream<WebSocketMessage> get messageStream => _messageController.stream;
  
  Future<void> connect(String tenantId, String accessToken) async {
    final uri = Uri.parse('wss://$tenantId.fc-china.com/ws/customer');
    
    _channel = IOWebSocketChannel.connect(uri);
    
    // Send authentication
    _channel!.sink.add(jsonEncode({
      'type': 'auth',
      'token': accessToken,
    }));
    
    // Listen for messages
    _channel!.stream.listen(
      (data) {
        final message = WebSocketMessage.fromJson(jsonDecode(data));
        _messageController.add(message);
      },
      onError: (error) {
        print('WebSocket error: $error');
        _reconnect(tenantId, accessToken);
      },
      onDone: () {
        print('WebSocket connection closed');
        _reconnect(tenantId, accessToken);
      },
    );
  }
  
  void sendMessage(Map<String, dynamic> message) {
    if (_channel != null) {
      _channel!.sink.add(jsonEncode(message));
    }
  }
}
```

### Push Notifications
```dart
// lib/services/notification_service.dart
class NotificationService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  
  Future<void> initialize() async {
    // Request permission
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
    
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      // Get FCM token
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        await _registerTokenWithServer(token);
      }
      
      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      
      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    }
  }
  
  Future<void> _registerTokenWithServer(String token) async {
    // Register FCM token with FC-CHINA backend
    await ApiService().post('/notifications/register-token', {
      'fcm_token': token,
      'platform': Platform.isIOS ? 'ios' : 'android',
    });
  }
}
```

## Web Application

### Vue.js Architecture
```javascript
// src/main.js
import { createApp } from 'vue'
import { createStore } from 'vuex'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import store from './store'
import router from './router'

const app = createApp(App)
app.use(store)
app.use(router)
app.mount('#app')
```

### Component Structure
```vue
<!-- src/components/ProductCatalog.vue -->
<template>
  <div class="product-catalog">
    <div class="filters">
      <SearchInput v-model="searchQuery" @search="handleSearch" />
      <CategoryFilter v-model="selectedCategory" :categories="categories" />
    </div>
    
    <div class="products-grid">
      <ProductCard 
        v-for="product in products" 
        :key="product.id"
        :product="product"
        @add-to-cart="handleAddToCart"
        @view-details="handleViewDetails"
      />
    </div>
    
    <Pagination 
      :current-page="currentPage"
      :total-pages="totalPages"
      @page-change="handlePageChange"
    />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'ProductCatalog',
  data() {
    return {
      searchQuery: '',
      selectedCategory: '',
      currentPage: 1
    }
  },
  computed: {
    ...mapState('products', ['products', 'categories', 'totalPages', 'loading'])
  },
  methods: {
    ...mapActions('products', ['fetchProducts', 'searchProducts']),
    
    async handleSearch() {
      await this.searchProducts({
        query: this.searchQuery,
        category: this.selectedCategory,
        page: 1
      })
    }
  },
  async mounted() {
    await this.fetchProducts({ page: 1 })
  }
}
</script>
```

## Multi-language Support

### Chinese-First Internationalization Strategy
FC-CHINA prioritizes Chinese language support for factories operating in China while providing multi-language support for global customers.

#### Language Priority Order
1. **Chinese Simplified (zh-CN)**: PRIMARY - Factory interface default
2. **English (en)**: International customer interface
3. **Spanish (es)**: Latin American markets
4. **Arabic (ar)**: Middle Eastern markets
5. **French (fr)**: European and African markets

#### Chinese Localization Requirements
- **Default Language**: Chinese for all factory-facing interfaces
- **Time Zone**: China Standard Time (CST/UTC+8) as system default
- **Date Format**: Chinese format (YYYY年MM月DD日) for factory users
- **Currency**: Chinese Yuan (CNY) as primary currency
- **Number Format**: Chinese number formatting standards

### Internationalization Implementation
```dart
// lib/l10n/app_localizations.dart
class AppLocalizations {
  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();
  
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('zh', 'CN'), // Chinese Simplified
    Locale('ar', 'SA'), // Arabic
    Locale('es', 'ES'), // Spanish
    Locale('fr', 'FR'), // French
  ];
  
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }
  
  String get welcome => _localizedValues['welcome']!;
  String get products => _localizedValues['products']!;
  String get orders => _localizedValues['orders']!;
  // ... more translations
}
```

### Language Files
```json
// assets/l10n/en.json
{
  "welcome": "Welcome to FC-CHINA",
  "products": "Products",
  "orders": "Orders",
  "production_tracking": "Production Tracking",
  "messages": "Messages",
  "profile": "Profile",
  "search_products": "Search products...",
  "add_to_cart": "Add to Cart",
  "view_details": "View Details",
  "order_status": "Order Status",
  "in_production": "In Production",
  "completed": "Completed",
  "cancelled": "Cancelled"
}

// assets/l10n/zh.json
{
  "welcome": "欢迎来到FC-CHINA",
  "products": "产品",
  "orders": "订单",
  "production_tracking": "生产跟踪",
  "messages": "消息",
  "profile": "个人资料",
  "search_products": "搜索产品...",
  "add_to_cart": "加入购物车",
  "view_details": "查看详情",
  "order_status": "订单状态",
  "in_production": "生产中",
  "completed": "已完成",
  "cancelled": "已取消"
}
```

## Customer Support Features

### Help Desk Integration
```python
# fc_china/customer_portal/support.py
class CustomerSupport:
    def create_support_ticket(self, customer_id, tenant_id, issue_data):
        """Create support ticket for customer"""
        
        ticket = frappe.get_doc({
            'doctype': 'Issue',
            'customer': customer_id,
            'company': self.get_tenant_company(tenant_id),
            'subject': issue_data['subject'],
            'description': issue_data['description'],
            'issue_type': issue_data.get('type', 'General'),
            'priority': issue_data.get('priority', 'Medium'),
            'status': 'Open'
        })
        ticket.insert()
        
        # Notify factory support team
        self.notify_support_team(ticket, tenant_id)
        
        return ticket
```

### FAQ System
```python
class CustomerFAQ:
    def get_tenant_faqs(self, tenant_id, category=None):
        """Get FAQ articles for specific tenant"""
        
        filters = {
            'tenant_id': tenant_id,
            'published': 1
        }
        
        if category:
            filters['category'] = category
        
        return frappe.get_all('FAQ Article',
            filters=filters,
            fields=['title', 'content', 'category', 'helpful_count'],
            order_by='helpful_count desc'
        )
```

## Cross-References

- [Technical Architecture](./TECHNICAL-ARCHITECTURE.md) - System architecture overview
- [Multi-Tenant Design](./MULTI-TENANT-DESIGN.md) - Tenant isolation for customers
- [API Specifications](./API-SPECIFICATIONS.md) - Customer-facing API details
- [Manufacturing Extensions](./MANUFACTURING-EXTENSIONS.md) - Production visibility features
- [Development Standards](./DEVELOPMENT-STANDARDS.md) - Development guidelines
