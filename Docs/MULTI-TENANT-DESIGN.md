# FC-CHINA Multi-Tenant Design

## Overview

FC-CHINA implements a multi-tenant architecture that transforms ERPNext from a single-company ERP into a SaaS platform supporting thousands of factory tenants with complete data isolation. The design leverages ERPNext's existing Company model as the foundation for tenant separation while adding sophisticated tenant management capabilities.

## Multi-Tenancy Strategy

### Approach: Company-Based Tenant Isolation

**Rationale**: ERPNext already implements robust data separation through the Company field present in all business documents. This provides a solid foundation for multi-tenancy without requiring fundamental architectural changes.

**Benefits**:
- ✅ Preserves all existing ERPNext functionality
- ✅ Leverages proven data isolation mechanisms
- ✅ Maintains compatibility with ERPNext updates
- ✅ Minimal performance overhead
- ✅ Simplified backup and recovery procedures

## Tenant Architecture

### Tenant Hierarchy
```
Platform Level
├── Super Admin (Platform Management)
├── Tenant 1 (Factory A)
│   ├── Factory Admin
│   ├── Factory Staff
│   └── Customers (External Users)
├── Tenant 2 (Factory B)
│   ├── Factory Admin
│   ├── Factory Staff
│   └── Customers (External Users)
└── Tenant N (Factory N)
```

### Tenant Data Model

#### Core Tenant Entity
```python
# fc_china/tenant_management/doctype/fc_tenant/fc_tenant.py
class FCTenant(Document):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    
    def validate(self):
        self.validate_subdomain_uniqueness()
        self.validate_company_association()
    
    def on_insert(self):
        self.create_tenant_infrastructure()
    
    def create_tenant_infrastructure(self):
        """Create all necessary infrastructure for new tenant"""
        # Create ERPNext Company
        company = self.create_company()
        
        # Setup default accounts and cost centers
        self.setup_default_accounts(company)
        
        # Create default warehouses
        self.setup_default_warehouses(company)
        
        # Setup manufacturing settings
        self.setup_manufacturing_defaults(company)
        
        # Create factory admin user
        self.create_factory_admin()
```

#### Tenant Configuration Schema
```json
{
  "tenant_id": "factory_001",
  "company_name": "ABC Manufacturing Ltd",
  "subdomain": "abc-manufacturing",
  "factory_details": {
    "name": "ABC Manufacturing",
    "industry": "Electronics",
    "location": "Shenzhen, China",
    "established": "2010",
    "employees": 500,
    "certifications": ["ISO9001", "ISO14001"]
  },
  "subscription": {
    "plan": "professional",
    "status": "active",
    "billing_cycle": "monthly",
    "features": ["production_tracking", "customer_portal", "api_access"]
  },
  "branding": {
    "logo_url": "/files/tenants/factory_001/logo.png",
    "primary_color": "#1976d2",
    "secondary_color": "#424242",
    "custom_domain": "portal.abcmanufacturing.com"
  },
  "settings": {
    "timezone": "Asia/Shanghai",
    "currency": "CNY",
    "language": "zh",
    "date_format": "dd-mm-yyyy"
  }
}
```

## Data Isolation Implementation

### Database-Level Isolation

#### Existing ERPNext Tables (Leveraged)
All ERPNext tables already include company field for data separation:

```sql
-- Example: Work Order table with company isolation
SELECT * FROM `tabWork Order` 
WHERE company = 'ABC Manufacturing Ltd'
AND customer = 'Global Customer Inc';

-- Stock Ledger Entry with company isolation
SELECT * FROM `tabStock Ledger Entry`
WHERE company = 'ABC Manufacturing Ltd'
AND posting_date BETWEEN '2024-01-01' AND '2024-01-31';
```

#### New FC-China Tables (Custom)
```sql
-- Tenant management table
CREATE TABLE `tabFC Tenant` (
    `name` varchar(140) NOT NULL PRIMARY KEY,
    `tenant_id` varchar(50) UNIQUE NOT NULL,
    `company` varchar(140) NOT NULL,
    `subdomain` varchar(100) UNIQUE NOT NULL,
    `status` enum('active', 'suspended', 'inactive') DEFAULT 'active',
    `created_date` datetime NOT NULL,
    `subscription_plan` varchar(50),
    `custom_domain` varchar(200),
    `branding_config` json,
    `settings` json,
    INDEX `idx_tenant_id` (`tenant_id`),
    INDEX `idx_subdomain` (`subdomain`),
    FOREIGN KEY (`company`) REFERENCES `tabCompany`(`name`) ON DELETE CASCADE
);

-- Customer-Factory access control
CREATE TABLE `tabCustomer Factory Access` (
    `name` varchar(140) NOT NULL PRIMARY KEY,
    `customer` varchar(140) NOT NULL,
    `factory_company` varchar(140) NOT NULL,
    `tenant_id` varchar(50) NOT NULL,
    `access_level` enum('basic', 'standard', 'premium') DEFAULT 'standard',
    `permissions` json,
    `granted_date` datetime NOT NULL,
    `expires_date` datetime,
    `status` enum('active', 'suspended', 'revoked') DEFAULT 'active',
    UNIQUE KEY `unique_customer_factory` (`customer`, `factory_company`),
    FOREIGN KEY (`customer`) REFERENCES `tabCustomer`(`name`),
    FOREIGN KEY (`factory_company`) REFERENCES `tabCompany`(`name`),
    FOREIGN KEY (`tenant_id`) REFERENCES `tabFC Tenant`(`tenant_id`)
);
```

### Application-Level Isolation

#### Tenant Context Middleware
```python
# fc_china/tenant_management/middleware.py
class TenantContextMiddleware:
    def __init__(self):
        self.tenant_cache = {}
    
    def process_request(self, request):
        """Extract tenant context from request"""
        tenant_id = self.extract_tenant_from_request(request)
        
        if tenant_id:
            # Set tenant context for the request
            frappe.local.tenant_id = tenant_id
            frappe.local.tenant_company = self.get_tenant_company(tenant_id)
            
            # Apply tenant-specific settings
            self.apply_tenant_settings(tenant_id)
    
    def extract_tenant_from_request(self, request):
        """Extract tenant ID from subdomain or custom domain"""
        host = request.get_host()
        
        # Check for subdomain pattern: {tenant}.fc-china.com
        if host.endswith('.fc-china.com'):
            return host.split('.')[0]
        
        # Check for custom domain mapping
        return self.get_tenant_by_custom_domain(host)
    
    def apply_tenant_settings(self, tenant_id):
        """Apply tenant-specific configurations"""
        tenant_settings = self.get_tenant_settings(tenant_id)
        
        # Set timezone, currency, language, etc.
        frappe.local.timezone = tenant_settings.get('timezone')
        frappe.local.currency = tenant_settings.get('currency')
        frappe.local.lang = tenant_settings.get('language')
```

#### Query Filtering
```python
# Automatic company filtering for all queries
class TenantQueryFilter:
    @staticmethod
    def apply_company_filter(doctype, filters=None):
        """Automatically add company filter to all queries"""
        if not filters:
            filters = {}
        
        # Get current tenant's company
        tenant_company = frappe.local.get('tenant_company')
        if tenant_company and frappe.get_meta(doctype).has_field('company'):
            filters['company'] = tenant_company
        
        return filters

# Usage in API endpoints
@frappe.whitelist()
def get_work_orders(filters=None):
    filters = TenantQueryFilter.apply_company_filter('Work Order', filters)
    return frappe.get_list('Work Order', filters=filters)
```

## User Management & Authentication

### Tenant-Scoped User Roles

#### Role Hierarchy
```python
# fc_china/tenant_management/roles.py
TENANT_ROLES = {
    'Platform Super Admin': {
        'level': 'platform',
        'permissions': ['manage_all_tenants', 'system_settings', 'billing_management']
    },
    'Factory Admin': {
        'level': 'tenant',
        'permissions': ['manage_factory', 'manage_users', 'view_analytics', 'customer_management']
    },
    'Factory Staff': {
        'level': 'tenant',
        'permissions': ['manufacturing_operations', 'inventory_management', 'customer_communication']
    },
    'Factory Customer': {
        'level': 'external',
        'permissions': ['view_orders', 'track_production', 'communicate', 'download_documents']
    }
}
```

#### Authentication Flow
```python
# fc_china/customer_portal/auth.py
class CustomerAuthentication:
    def authenticate_customer(self, email, password, tenant_id):
        """Authenticate customer against specific tenant"""
        
        # Validate tenant exists and is active
        tenant = self.validate_tenant(tenant_id)
        if not tenant:
            raise AuthenticationError("Invalid tenant")
        
        # Find customer in tenant's company context
        customer = frappe.db.get_value(
            'Customer',
            {'email_id': email, 'company': tenant.company},
            ['name', 'customer_name', 'disabled']
        )
        
        if not customer or customer.disabled:
            raise AuthenticationError("Customer not found or disabled")
        
        # Validate password (using Frappe's user authentication)
        user = frappe.get_doc('User', email)
        if not user.check_password(password):
            raise AuthenticationError("Invalid credentials")
        
        # Check customer has access to this factory
        access = self.check_customer_factory_access(customer.name, tenant.company)
        if not access:
            raise AuthenticationError("Access denied to this factory")
        
        # Generate JWT token with tenant context
        token_payload = {
            'user_id': user.name,
            'customer_id': customer.name,
            'tenant_id': tenant_id,
            'company': tenant.company,
            'user_type': 'customer',
            'permissions': access.permissions,
            'exp': datetime.utcnow() + timedelta(hours=24)
        }
        
        return jwt.encode(token_payload, frappe.conf.secret_key, algorithm='HS256')
```

## Tenant Provisioning

### Automated Tenant Creation
```python
# fc_china/tenant_management/provisioning.py
class TenantProvisioner:
    def create_new_tenant(self, tenant_data):
        """Complete tenant provisioning workflow"""
        
        try:
            # 1. Create tenant record
            tenant = self.create_tenant_record(tenant_data)
            
            # 2. Create ERPNext company
            company = self.create_company(tenant_data)
            
            # 3. Setup chart of accounts
            self.setup_chart_of_accounts(company)
            
            # 4. Create default warehouses
            self.setup_warehouses(company)
            
            # 5. Configure manufacturing settings
            self.setup_manufacturing_settings(company)
            
            # 6. Create factory admin user
            admin_user = self.create_factory_admin(tenant_data, company)
            
            # 7. Setup default customer groups and territories
            self.setup_customer_defaults(company)
            
            # 8. Configure tenant-specific settings
            self.apply_tenant_configuration(tenant, tenant_data)
            
            # 9. Send welcome email to factory admin
            self.send_welcome_email(admin_user, tenant)
            
            return tenant
            
        except Exception as e:
            # Rollback on failure
            self.rollback_tenant_creation(tenant_data.get('tenant_id'))
            raise TenantProvisioningError(f"Failed to create tenant: {str(e)}")
    
    def setup_manufacturing_settings(self, company):
        """Configure manufacturing module for new tenant"""
        manufacturing_settings = frappe.get_doc({
            'doctype': 'Manufacturing Settings',
            'company': company,
            'material_consumption': 'Backflush',
            'backflush_raw_materials_based_on': 'BOM',
            'update_bom_costs_automatically': 1,
            'default_wip_warehouse': f'Work In Progress - {company[:3]}',
            'default_fg_warehouse': f'Finished Goods - {company[:3]}',
            'overproduction_percentage_for_work_order': 10
        })
        manufacturing_settings.insert()
```

### Tenant Migration and Backup

#### Data Export/Import
```python
class TenantDataManager:
    def export_tenant_data(self, tenant_id):
        """Export all tenant data for backup or migration"""
        tenant = self.get_tenant(tenant_id)
        
        export_data = {
            'tenant_info': tenant.as_dict(),
            'company_data': self.export_company_data(tenant.company),
            'master_data': self.export_master_data(tenant.company),
            'transaction_data': self.export_transaction_data(tenant.company),
            'files': self.export_tenant_files(tenant_id)
        }
        
        return export_data
    
    def import_tenant_data(self, export_data, new_tenant_id):
        """Import tenant data to new tenant"""
        # Create new tenant with imported configuration
        # Import all master and transaction data
        # Restore files and media
        pass
```

## Subdomain and Domain Management

### Subdomain Routing
```python
# fc_china/tenant_management/routing.py
class TenantRouter:
    def __init__(self):
        self.tenant_cache = {}
    
    def resolve_tenant_from_host(self, host):
        """Resolve tenant from hostname"""
        
        # Cache lookup first
        if host in self.tenant_cache:
            return self.tenant_cache[host]
        
        tenant = None
        
        # Pattern 1: Subdomain - tenant.fc-china.com
        if host.endswith('.fc-china.com'):
            subdomain = host.replace('.fc-china.com', '')
            tenant = frappe.db.get_value('FC Tenant', {'subdomain': subdomain}, 'name')
        
        # Pattern 2: Custom domain - portal.factory.com
        else:
            tenant = frappe.db.get_value('FC Tenant', {'custom_domain': host}, 'name')
        
        # Cache the result
        if tenant:
            self.tenant_cache[host] = tenant
        
        return tenant
```

### SSL Certificate Management
```python
class SSLManager:
    def provision_ssl_certificate(self, tenant_id, domain):
        """Automatically provision SSL certificate for tenant domain"""
        # Integration with Let's Encrypt or cloud provider
        # Automatic certificate renewal
        pass
```

## Performance Optimization

### Tenant-Aware Caching
```python
# fc_china/tenant_management/cache.py
class TenantCache:
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def get_cache_key(self, tenant_id, key):
        """Generate tenant-specific cache key"""
        return f"tenant:{tenant_id}:{key}"
    
    def get_tenant_data(self, tenant_id, key):
        """Get cached data for specific tenant"""
        cache_key = self.get_cache_key(tenant_id, key)
        data = self.redis_client.get(cache_key)
        return json.loads(data) if data else None
    
    def set_tenant_data(self, tenant_id, key, data, expiry=3600):
        """Cache data for specific tenant"""
        cache_key = self.get_cache_key(tenant_id, key)
        self.redis_client.setex(cache_key, expiry, json.dumps(data))
```

### Database Optimization
```sql
-- Optimized indexes for multi-tenant queries
CREATE INDEX idx_company_customer ON `tabSales Order` (company, customer);
CREATE INDEX idx_company_item ON `tabStock Ledger Entry` (company, item_code, posting_date);
CREATE INDEX idx_company_status ON `tabWork Order` (company, status, creation);

-- Partitioning strategy for large tables (if needed)
ALTER TABLE `tabStock Ledger Entry` 
PARTITION BY HASH(CRC32(company)) PARTITIONS 10;
```

## Security Considerations

### Tenant Isolation Security
- **Database Level**: Company field filtering prevents cross-tenant data access
- **Application Level**: Middleware enforces tenant context
- **File System**: Tenant-specific file storage paths
- **API Level**: JWT tokens include tenant context validation

### Security Monitoring
```python
class TenantSecurityMonitor:
    def log_cross_tenant_attempt(self, user_id, attempted_tenant, actual_tenant):
        """Log potential security breach attempts"""
        security_log = {
            'timestamp': datetime.utcnow(),
            'user_id': user_id,
            'attempted_tenant': attempted_tenant,
            'actual_tenant': actual_tenant,
            'ip_address': frappe.local.request.environ.get('REMOTE_ADDR'),
            'user_agent': frappe.local.request.environ.get('HTTP_USER_AGENT'),
            'severity': 'HIGH'
        }
        
        # Log to security monitoring system
        self.write_security_log(security_log)
        
        # Alert administrators
        self.send_security_alert(security_log)
```

## Cross-References

- [Technical Architecture](./TECHNICAL-ARCHITECTURE.md) - Overall system architecture
- [API Specifications](./API-SPECIFICATIONS.md) - Tenant-aware API design
- [Customer Integration](./CUSTOMER-INTEGRATION.md) - Customer authentication and access
- [Deployment Guide](./DEPLOYMENT-GUIDE.md) - Multi-tenant deployment strategies
- [Development Standards](./DEVELOPMENT-STANDARDS.md) - Security and coding standards
