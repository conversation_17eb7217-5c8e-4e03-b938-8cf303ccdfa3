# FC-CHINA Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the FC-CHINA B2B marketplace platform in production environments. The deployment strategy focuses on scalability, security, and high availability to support thousands of factory tenants and customers.

## Infrastructure Architecture

### Production Environment Structure
```
┌─────────────────────────────────────────────────────────────┐
│                    Load Balancer (Nginx)                    │
│                  SSL Termination & Routing                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│                Application Tier                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   App 1     │  │   App 2     │  │   App N     │        │
│  │ ERPNext +   │  │ ERPNext +   │  │ ERPNext +   │        │
│  │ FC-China    │  │ FC-China    │  │ FC-China    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────┼───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│                 Database Tier                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Master    │  │   Slave 1   │  │   Slave 2   │        │
│  │   MariaDB   │  │   MariaDB   │  │   MariaDB   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────┼───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│                 Cache & Queue                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    Redis    │  │   Celery    │  │ Elasticsearch│        │
│  │   Cluster   │  │   Workers   │  │   Cluster   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## Server Requirements

### Minimum Production Specifications

#### Application Servers (3+ instances)
- **CPU**: 8 cores (Intel Xeon or AMD EPYC)
- **RAM**: 32 GB
- **Storage**: 500 GB SSD
- **Network**: 1 Gbps
- **OS**: Ubuntu 20.04 LTS or CentOS 8

#### Database Servers (3 instances - 1 master, 2 slaves)
- **CPU**: 16 cores
- **RAM**: 64 GB
- **Storage**: 2 TB NVMe SSD (with RAID 10)
- **Network**: 10 Gbps
- **OS**: Ubuntu 20.04 LTS

#### Load Balancer (2 instances for HA)
- **CPU**: 4 cores
- **RAM**: 16 GB
- **Storage**: 200 GB SSD
- **Network**: 10 Gbps
- **OS**: Ubuntu 20.04 LTS

#### Cache & Queue Servers
- **Redis Cluster**: 3 nodes, 8 cores, 32 GB RAM each
- **Celery Workers**: 4 nodes, 8 cores, 16 GB RAM each
- **Elasticsearch**: 3 nodes, 8 cores, 32 GB RAM each

## Docker Deployment

### Docker Compose Configuration
```yaml
# docker-compose.production.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/sites:/etc/nginx/sites-enabled
    depends_on:
      - erpnext-app
    networks:
      - fc-china-network

  erpnext-app:
    build:
      context: .
      dockerfile: Dockerfile.production
    environment:
      - FRAPPE_SITE_NAME_HEADER=fc-china.com
      - DB_HOST=mariadb-master
      - DB_PORT=3306
      - REDIS_CACHE_URL=redis://redis-cluster:6379/0
      - REDIS_QUEUE_URL=redis://redis-cluster:6379/1
      - REDIS_SOCKETIO_URL=redis://redis-cluster:6379/2
    volumes:
      - ./sites:/home/<USER>/frappe-bench/sites
      - ./logs:/home/<USER>/frappe-bench/logs
    depends_on:
      - mariadb-master
      - redis-cluster
    networks:
      - fc-china-network
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G

  mariadb-master:
    image: mariadb:10.6
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
      - MYSQL_DATABASE=fc_china
      - MYSQL_USER=fc_china_user
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mariadb-master-data:/var/lib/mysql
      - ./mariadb/master.cnf:/etc/mysql/conf.d/master.cnf
    networks:
      - fc-china-network
    command: --server-id=1 --log-bin=mysql-bin --binlog-format=ROW

  mariadb-slave-1:
    image: mariadb:10.6
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
      - MYSQL_DATABASE=fc_china
      - MYSQL_USER=fc_china_user
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mariadb-slave-1-data:/var/lib/mysql
      - ./mariadb/slave.cnf:/etc/mysql/conf.d/slave.cnf
    networks:
      - fc-china-network
    command: --server-id=2 --relay-log=mysql-relay-bin

  redis-cluster:
    image: redis:7-alpine
    volumes:
      - redis-data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - fc-china-network
    command: redis-server /usr/local/etc/redis/redis.conf

  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.production
    environment:
      - DB_HOST=mariadb-master
      - REDIS_CACHE_URL=redis://redis-cluster:6379/0
      - REDIS_QUEUE_URL=redis://redis-cluster:6379/1
    volumes:
      - ./sites:/home/<USER>/frappe-bench/sites
    depends_on:
      - mariadb-master
      - redis-cluster
    networks:
      - fc-china-network
    command: bench worker --queue default,long,short
    deploy:
      replicas: 4

  elasticsearch:
    image: elasticsearch:7.17.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - fc-china-network

volumes:
  mariadb-master-data:
  mariadb-slave-1-data:
  redis-data:
  elasticsearch-data:

networks:
  fc-china-network:
    driver: bridge
```

### Production Dockerfile
```dockerfile
# Dockerfile.production
FROM frappe/erpnext:v14.latest

USER root

# Install FC-China custom app
RUN cd /home/<USER>/frappe-bench && \
    bench get-app fc_china https://github.com/your-org/fc-china.git && \
    bench build --production

# Install additional dependencies
COPY requirements.txt /tmp/
RUN pip install -r /tmp/requirements.txt

# Copy custom configurations
COPY ./config/production_config.py /home/<USER>/frappe-bench/sites/common_site_config.json

# Set proper permissions
RUN chown -R frappe:frappe /home/<USER>/frappe-bench

USER frappe

# Install FC-China app on all sites
RUN cd /home/<USER>/frappe-bench && \
    bench --site all install-app fc_china

EXPOSE 8000

CMD ["bench", "serve", "--port", "8000", "--host", "0.0.0.0"]
```

## Nginx Configuration

### Main Nginx Configuration
```nginx
# nginx/nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/javascript application/xml+rss 
               application/json application/xml;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    # Upstream servers
    upstream erpnext_backend {
        least_conn;
        server erpnext-app-1:8000 max_fails=3 fail_timeout=30s;
        server erpnext-app-2:8000 max_fails=3 fail_timeout=30s;
        server erpnext-app-3:8000 max_fails=3 fail_timeout=30s;
    }

    # Include site configurations
    include /etc/nginx/sites-enabled/*;
}
```

### Site-Specific Configuration
```nginx
# nginx/sites/fc-china.conf
server {
    listen 80;
    server_name *.fc-china.com fc-china.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name *.fc-china.com fc-china.com;

    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/fc-china.com.crt;
    ssl_certificate_key /etc/nginx/ssl/fc-china.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # API rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://erpnext_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Login rate limiting
    location /api/method/login {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://erpnext_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://erpnext_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        proxy_pass http://erpnext_backend;
    }

    # File uploads
    location /files/ {
        expires 1y;
        add_header Cache-Control "public";
        proxy_pass http://erpnext_backend;
    }

    # Default location
    location / {
        proxy_pass http://erpnext_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }
}
```

## Database Configuration

### MariaDB Master Configuration
```ini
# mariadb/master.cnf
[mysqld]
# Server ID
server-id = 1

# Binary logging
log-bin = mysql-bin
binlog-format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# Performance tuning
innodb_buffer_pool_size = 32G
innodb_log_file_size = 2G
innodb_flush_log_at_trx_commit = 1
innodb_file_per_table = 1

# Connection settings
max_connections = 1000
max_connect_errors = 1000000

# Query cache
query_cache_type = 1
query_cache_size = 256M

# Slow query log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Multi-tenant optimization
innodb_thread_concurrency = 16
innodb_read_io_threads = 8
innodb_write_io_threads = 8
```

### Database Replication Setup
```sql
-- On Master Server
CREATE USER 'replication'@'%' IDENTIFIED BY 'replication_password';
GRANT REPLICATION SLAVE ON *.* TO 'replication'@'%';
FLUSH PRIVILEGES;

-- Get master status
SHOW MASTER STATUS;

-- On Slave Servers
CHANGE MASTER TO
    MASTER_HOST='mariadb-master',
    MASTER_USER='replication',
    MASTER_PASSWORD='replication_password',
    MASTER_LOG_FILE='mysql-bin.000001',
    MASTER_LOG_POS=154;

START SLAVE;
SHOW SLAVE STATUS\G
```

## Redis Configuration

### Redis Cluster Setup
```conf
# redis/redis.conf
# Network
bind 0.0.0.0
port 6379
protected-mode no

# Memory
maxmemory 16gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Performance
tcp-keepalive 300
timeout 0
tcp-backlog 511
databases 16

# Cluster configuration (if using Redis Cluster)
# cluster-enabled yes
# cluster-config-file nodes.conf
# cluster-node-timeout 15000
```

## SSL Certificate Management

### Let's Encrypt with Certbot
```bash
#!/bin/bash
# scripts/ssl-setup.sh

# Install certbot
apt-get update
apt-get install -y certbot python3-certbot-nginx

# Generate wildcard certificate for *.fc-china.com
certbot certonly \
    --manual \
    --preferred-challenges=dns \
    --email <EMAIL> \
    --server https://acme-v02.api.letsencrypt.org/directory \
    --agree-tos \
    -d *.fc-china.com \
    -d fc-china.com

# Setup auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -

# Copy certificates to nginx directory
cp /etc/letsencrypt/live/fc-china.com/fullchain.pem /etc/nginx/ssl/fc-china.com.crt
cp /etc/letsencrypt/live/fc-china.com/privkey.pem /etc/nginx/ssl/fc-china.com.key

# Reload nginx
systemctl reload nginx
```

## Monitoring and Logging

### Prometheus Configuration
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'erpnext'
    static_configs:
      - targets: ['erpnext-app-1:8000', 'erpnext-app-2:8000', 'erpnext-app-3:8000']
    metrics_path: '/api/method/frappe.utils.prometheus.metrics'

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']

  - job_name: 'mariadb'
    static_configs:
      - targets: ['mariadb-master:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-cluster:9121']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### Grafana Dashboard
```json
{
  "dashboard": {
    "title": "FC-CHINA Production Monitoring",
    "panels": [
      {
        "title": "Application Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "mysql_global_status_threads_connected",
            "legendFormat": "Active Connections"
          }
        ]
      },
      {
        "title": "Tenant Activity",
        "type": "table",
        "targets": [
          {
            "expr": "sum by (tenant_id) (rate(http_requests_total[5m]))",
            "legendFormat": "Requests per second"
          }
        ]
      }
    ]
  }
}
```

## Backup Strategy

### Automated Backup Script
```bash
#!/bin/bash
# scripts/backup.sh

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Database backup
mysqldump --single-transaction --routines --triggers \
    --all-databases > $BACKUP_DIR/db_backup_$DATE.sql

# Compress database backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz /home/<USER>/frappe-bench/sites/*/private/files
tar -czf $BACKUP_DIR/public_backup_$DATE.tar.gz /home/<USER>/frappe-bench/sites/*/public/files

# Upload to S3 (optional)
aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql.gz s3://fc-china-backups/database/
aws s3 cp $BACKUP_DIR/files_backup_$DATE.tar.gz s3://fc-china-backups/files/
aws s3 cp $BACKUP_DIR/public_backup_$DATE.tar.gz s3://fc-china-backups/public/

# Cleanup old backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

echo "Backup completed: $DATE"
```

## Deployment Scripts

### Automated Deployment Script
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

ENVIRONMENT=${1:-production}
VERSION=${2:-latest}

echo "Deploying FC-CHINA version $VERSION to $ENVIRONMENT"

# Pull latest code
git fetch origin
git checkout $VERSION

# Build new Docker images
docker-compose -f docker-compose.$ENVIRONMENT.yml build

# Run database migrations
docker-compose -f docker-compose.$ENVIRONMENT.yml run --rm erpnext-app bench migrate

# Update static assets
docker-compose -f docker-compose.$ENVIRONMENT.yml run --rm erpnext-app bench build --production

# Rolling update
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d --no-deps erpnext-app

# Health check
sleep 30
curl -f http://localhost/api/method/ping || exit 1

echo "Deployment completed successfully"
```

## Security Hardening

### Firewall Configuration
```bash
#!/bin/bash
# scripts/firewall-setup.sh

# Reset iptables
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X

# Default policies
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow loopback
iptables -A INPUT -i lo -j ACCEPT

# Allow established connections
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# Allow SSH (change port as needed)
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# Allow HTTP/HTTPS
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Allow database connections (internal only)
iptables -A INPUT -p tcp --dport 3306 -s 10.0.0.0/8 -j ACCEPT

# Allow Redis connections (internal only)
iptables -A INPUT -p tcp --dport 6379 -s 10.0.0.0/8 -j ACCEPT

# Save rules
iptables-save > /etc/iptables/rules.v4
```

## Performance Optimization

### Application-Level Optimizations
```python
# fc_china/performance/optimizations.py
class PerformanceOptimizations:
    @staticmethod
    def setup_caching():
        """Setup Redis caching for frequently accessed data"""
        
        # Cache tenant configurations
        frappe.cache().set_value("tenant_configs", get_all_tenant_configs(), expires_in_sec=3600)
        
        # Cache product catalogs
        for tenant in get_active_tenants():
            catalog = get_tenant_product_catalog(tenant.tenant_id)
            frappe.cache().set_value(f"catalog_{tenant.tenant_id}", catalog, expires_in_sec=1800)
    
    @staticmethod
    def optimize_database_queries():
        """Add database indexes for multi-tenant queries"""
        
        indexes = [
            "CREATE INDEX idx_company_customer ON `tabSales Order` (company, customer)",
            "CREATE INDEX idx_company_item ON `tabStock Ledger Entry` (company, item_code, posting_date)",
            "CREATE INDEX idx_tenant_status ON `tabFC Tenant` (tenant_id, status)",
            "CREATE INDEX idx_customer_factory ON `tabCustomer Factory Access` (customer, factory_company, status)"
        ]
        
        for index in indexes:
            try:
                frappe.db.sql(index)
            except Exception as e:
                frappe.log_error(f"Index creation failed: {e}")
```

## Cross-References

- [Technical Architecture](./TECHNICAL-ARCHITECTURE.md) - System architecture overview
- [Multi-Tenant Design](./MULTI-TENANT-DESIGN.md) - Multi-tenancy implementation
- [API Specifications](./API-SPECIFICATIONS.md) - API endpoints and authentication
- [Development Standards](./DEVELOPMENT-STANDARDS.md) - Development and security guidelines
- [FC-CHINA Roadmap](./FC-CHINA-ROADMAP.md) - Implementation phases and timeline
