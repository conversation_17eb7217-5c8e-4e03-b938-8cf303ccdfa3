# FC-CHINA API Specifications

## API Overview

FC-CHINA provides a comprehensive RESTful API that enables customer applications to interact with Chinese factory data while maintaining strict tenant isolation. The API is designed with mobile-first principles, supporting real-time communication, production transparency, and Chinese business practices. **IMPORTANT: FC-CHINA does NOT process payments - only tracks payment references and status.**

## Base URL Structure

### Tenant-Specific Endpoints
```
https://{tenant}.fc-china.com/api/v1/
https://api.fc-china.com/v1/tenant/{tenant_id}/
```

### Platform Management Endpoints
```
https://admin.fc-china.com/api/v1/platform/
```

## Authentication

### JWT-Based Authentication
All API requests require JWT authentication with tenant context.

#### Authentication Flow
```http
POST /api/v1/auth/customer/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure_password",
  "tenant_id": "factory_001"
}
```

#### Response
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400,
    "user": {
      "id": "CUST-2024-001",
      "name": "John Smith",
      "email": "<EMAIL>",
      "type": "customer",
      "tenant_id": "factory_001",
      "permissions": ["view_orders", "track_production", "communicate"]
    }
  }
}
```

#### Token Usage
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-Tenant-ID: factory_001
```

## Core API Endpoints

### 1. Authentication APIs

#### Customer Login
```http
POST /api/v1/auth/customer/login
```

**Request Body:**
```json
{
  "email": "string",
  "password": "string",
  "tenant_id": "string"
}
```

**Response:**
```json
{
  "success": boolean,
  "data": {
    "access_token": "string",
    "refresh_token": "string",
    "expires_in": number,
    "user": {
      "id": "string",
      "name": "string",
      "email": "string",
      "type": "customer",
      "tenant_id": "string",
      "permissions": ["string"]
    }
  }
}
```

#### Token Refresh
```http
POST /api/v1/auth/refresh
```

**Request Body:**
```json
{
  "refresh_token": "string"
}
```

#### Customer Registration
```http
POST /api/v1/auth/customer/register
```

**Request Body:**
```json
{
  "email": "string",
  "password": "string",
  "name": "string",
  "company": "string",
  "phone": "string",
  "tenant_id": "string",
  "invitation_code": "string"
}
```

### 2. Customer Profile APIs

#### Get Customer Profile
```http
GET /api/v1/customer/profile
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "CUST-2024-001",
    "name": "John Smith",
    "email": "<EMAIL>",
    "company": "Global Imports Ltd",
    "phone": "******-0123",
    "address": {
      "street": "123 Business Ave",
      "city": "New York",
      "country": "United States",
      "postal_code": "10001"
    },
    "factory_access": [
      {
        "tenant_id": "factory_001",
        "factory_name": "ABC Manufacturing",
        "access_level": "standard",
        "granted_date": "2024-01-15T10:30:00Z"
      }
    ],
    "preferences": {
      "language": "zh-CN",
      "timezone": "Asia/Shanghai",
      "currency": "CNY",
      "notifications": {
        "email": true,
        "push": true,
        "sms": false
      }
    }
  }
}
```

#### Update Customer Profile
```http
PUT /api/v1/customer/profile
```

**Request Body:**
```json
{
  "name": "string",
  "company": "string",
  "phone": "string",
  "address": {
    "street": "string",
    "city": "string",
    "country": "string",
    "postal_code": "string"
  },
  "preferences": {
    "language": "string",
    "timezone": "string",
    "currency": "string",
    "notifications": {
      "email": boolean,
      "push": boolean,
      "sms": boolean
    }
  }
}
```

### 3. Product Catalog APIs

#### List Products
```http
GET /api/v1/products?category={category}&search={query}&page={page}&limit={limit}
```

**Query Parameters:**
- `category` (optional): Product category filter
- `search` (optional): Search query for product name/description
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `sort` (optional): Sort field (name, price, created_date)
- `order` (optional): Sort order (asc, desc)

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "ITEM-2024-001",
        "name": "Wireless Bluetooth Headphones",
        "description": "High-quality wireless headphones with noise cancellation",
        "category": "Electronics",
        "images": [
          "https://cdn.fc-china.com/products/item-001-1.jpg",
          "https://cdn.fc-china.com/products/item-001-2.jpg"
        ],
        "specifications": {
          "color": ["Black", "White", "Blue"],
          "battery_life": "30 hours",
          "connectivity": "Bluetooth 5.0",
          "weight": "250g"
        },
        "pricing": {
          "currency": "USD",
          "unit_price": 45.00,
          "min_order_qty": 100,
          "bulk_pricing": [
            {"qty": 500, "price": 42.00},
            {"qty": 1000, "price": 38.00}
          ]
        },
        "availability": {
          "in_stock": true,
          "stock_qty": 5000,
          "lead_time_days": 15
        },
        "created_date": "2024-01-10T08:00:00Z",
        "updated_date": "2024-01-20T14:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total_items": 150,
      "total_pages": 8,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

#### Get Product Details
```http
GET /api/v1/products/{product_id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "ITEM-2024-001",
    "name": "Wireless Bluetooth Headphones",
    "description": "Detailed product description...",
    "category": "Electronics",
    "images": ["url1", "url2"],
    "specifications": {},
    "pricing": {},
    "availability": {},
    "certifications": ["CE", "FCC", "RoHS"],
    "manufacturing_details": {
      "production_capacity": "10000 units/month",
      "quality_standards": ["ISO9001"],
      "customization_options": ["Logo printing", "Color customization"]
    },
    "related_products": ["ITEM-2024-002", "ITEM-2024-003"]
  }
}
```

#### Product Search
```http
GET /api/v1/products/search?q={query}&filters={filters}
```

### 4. Order Management APIs

#### Create Order
```http
POST /api/v1/orders
```

**Request Body:**
```json
{
  "items": [
    {
      "product_id": "ITEM-2024-001",
      "quantity": 500,
      "unit_price": 42.00,
      "specifications": {
        "color": "Black",
        "logo": "Custom Logo"
      }
    }
  ],
  "delivery_address": {
    "name": "John Smith",
    "company": "Global Imports Ltd",
    "street": "123 Business Ave",
    "city": "New York",
    "country": "United States",
    "postal_code": "10001",
    "phone": "******-0123"
  },
  "delivery_date": "2024-03-15",
  "special_instructions": "Please ensure careful packaging",
  "payment_reference": "ALI_PAY_REF_123456789",
  "payment_status": "pending_confirmation",
  "payment_method": "alipay"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "order_id": "SO-2024-001",
    "status": "draft",
    "total_amount": 21000.00,
    "currency": "USD",
    "estimated_delivery": "2024-03-15",
    "created_date": "2024-01-25T10:00:00Z"
  }
}
```

#### List Customer Orders
```http
GET /api/v1/orders?status={status}&page={page}&limit={limit}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "SO-2024-001",
        "status": "in_production",
        "total_amount": 21000.00,
        "currency": "USD",
        "order_date": "2024-01-25T10:00:00Z",
        "estimated_delivery": "2024-03-15",
        "items_count": 1,
        "production_progress": 65
      }
    ],
    "pagination": {}
  }
}
```

#### Get Order Details
```http
GET /api/v1/orders/{order_id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "SO-2024-001",
    "status": "in_production",
    "items": [],
    "delivery_address": {},
    "production_tracking": {
      "work_order": "WO-2024-001",
      "progress_percentage": 65,
      "current_stage": "Quality Testing",
      "milestones": [
        {
          "name": "Material Procurement",
          "status": "completed",
          "completion_date": "2024-02-01T09:00:00Z"
        },
        {
          "name": "Production Start",
          "status": "completed",
          "completion_date": "2024-02-05T08:00:00Z"
        },
        {
          "name": "Quality Testing",
          "status": "in_progress",
          "estimated_completion": "2024-02-20T17:00:00Z"
        }
      ]
    },
    "communication_thread": "THREAD-2024-001"
  }
}
```

### 5. Production Tracking APIs

#### Get Production Status
```http
GET /api/v1/orders/{order_id}/production
```

**Response:**
```json
{
  "success": true,
  "data": {
    "work_order": "WO-2024-001",
    "status": "in_progress",
    "progress_percentage": 65,
    "current_operation": "Quality Testing",
    "estimated_completion": "2024-03-10T17:00:00Z",
    "milestones": [
      {
        "id": "milestone_001",
        "name": "Material Procurement",
        "description": "Raw materials sourced and verified",
        "status": "completed",
        "completion_date": "2024-02-01T09:00:00Z",
        "photos": [
          "https://cdn.fc-china.com/production/wo-001-materials.jpg"
        ],
        "notes": "All materials passed quality inspection"
      }
    ],
    "quality_checkpoints": [
      {
        "checkpoint": "Incoming Material Inspection",
        "status": "passed",
        "date": "2024-02-01T10:00:00Z",
        "inspector": "QC Team A",
        "report_url": "https://cdn.fc-china.com/qc/report-001.pdf"
      }
    ],
    "photos": [
      {
        "url": "https://cdn.fc-china.com/production/wo-001-progress.jpg",
        "caption": "Production line setup",
        "timestamp": "2024-02-10T14:30:00Z"
      }
    ]
  }
}
```

### 6. Communication APIs

#### Get Communication Thread
```http
GET /api/v1/communication/{thread_id}/messages?page={page}&limit={limit}
```

#### Send Message
```http
POST /api/v1/communication/{thread_id}/messages
```

**Request Body:**
```json
{
  "message": "Could you provide an update on the production timeline?",
  "attachments": [
    {
      "filename": "specification_update.pdf",
      "url": "https://cdn.fc-china.com/uploads/spec-update.pdf"
    }
  ]
}
```

#### Upload File
```http
POST /api/v1/communication/upload
Content-Type: multipart/form-data
```

### 7. Notification APIs

#### Get Notifications
```http
GET /api/v1/notifications?unread_only={boolean}&page={page}&limit={limit}
```

#### Mark Notification as Read
```http
PUT /api/v1/notifications/{notification_id}/read
```

## Error Handling

### Standard Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "request_id": "req_123456789"
}
```

### Common Error Codes
- `AUTHENTICATION_FAILED` (401): Invalid credentials or token
- `AUTHORIZATION_DENIED` (403): Insufficient permissions
- `RESOURCE_NOT_FOUND` (404): Requested resource doesn't exist
- `VALIDATION_ERROR` (400): Invalid request parameters
- `TENANT_NOT_FOUND` (404): Invalid tenant ID
- `RATE_LIMIT_EXCEEDED` (429): Too many requests
- `INTERNAL_SERVER_ERROR` (500): Server error

## Rate Limiting

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

### Rate Limits by Endpoint Type
- **Authentication**: 10 requests per minute
- **Product Catalog**: 100 requests per minute
- **Order Management**: 50 requests per minute
- **Production Tracking**: 200 requests per minute
- **Communication**: 500 requests per minute

## Payment Reference Tracking API

**IMPORTANT**: FC-CHINA does NOT process payments. These endpoints only track payment references and status from external payment systems.

### Get Payment References
**Endpoint:** `GET /api/v1/orders/{order_id}/payment-references`

**Response:**
```json
{
  "success": true,
  "data": {
    "payment_references": [
      {
        "reference_id": "ALIPAY_2024_001234567",
        "payment_method": "alipay",
        "amount": 15000.00,
        "currency": "CNY",
        "status": "confirmed",
        "created_date": "2024-01-15T10:30:00+08:00",
        "confirmed_date": "2024-01-15T10:35:00+08:00",
        "external_transaction_id": "2024011522001234567890123456"
      },
      {
        "reference_id": "WECHAT_2024_001234568",
        "payment_method": "wechat_pay",
        "amount": 5000.00,
        "currency": "CNY",
        "status": "pending",
        "created_date": "2024-01-20T14:20:00+08:00"
      }
    ],
    "total_amount": 20000.00,
    "currency": "CNY",
    "payment_status": "partial"
  }
}
```

### Update Payment Reference Status
**Endpoint:** `PUT /api/v1/payment-references/{reference_id}/status`

**Request:**
```json
{
  "status": "confirmed",
  "external_transaction_id": "2024011522001234567890123456",
  "confirmed_date": "2024-01-15T10:35:00+08:00",
  "notes": "Payment confirmed via Alipay merchant portal"
}
```

## WebSocket API

### Connection
```javascript
const socket = new WebSocket('wss://factory_001.fc-china.com/ws/customer');
socket.onopen = function(event) {
    // Send authentication
    socket.send(JSON.stringify({
        type: 'auth',
        token: 'jwt_token_here'
    }));
};
```

### Message Types
```json
{
  "type": "production_update",
  "data": {
    "order_id": "SO-2024-001",
    "work_order": "WO-2024-001",
    "progress_percentage": 70,
    "milestone_completed": "Quality Testing",
    "photos": ["url1", "url2"]
  }
}
```

## Cross-References

- [Technical Architecture](./TECHNICAL-ARCHITECTURE.md) - API infrastructure details
- [Multi-Tenant Design](./MULTI-TENANT-DESIGN.md) - Tenant isolation in APIs
- [Customer Integration](./CUSTOMER-INTEGRATION.md) - Customer-facing features
- [Manufacturing Extensions](./MANUFACTURING-EXTENSIONS.md) - Production tracking APIs
- [Development Standards](./DEVELOPMENT-STANDARDS.md) - API development guidelines
