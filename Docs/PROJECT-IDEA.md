I want to transform the existing ERPNext system into a commercial B2B marketplace called "FC-CHINA" by leveraging and adapting what's already built while adding the missing customer relationship layer. Please analyze the current ERPNext codebase and make professional decisions on what to adapt, extend, and add.

\*\*PROJECT VISION:\*\*

Transform ERPNext from a single-company ERP into a multi-tenant B2B marketplace connecting Chinese factories (who keep their full manufacturing capabilities) with global customers through modern web and mobile interfaces.

\*\*BUSINESS MODEL:\*\*

\- Multi-tenant SaaS platform where each factory operates as an independent tenant

\- Factories maintain all their existing ERP functionality (manufacturing, inventory, accounting, etc.)

\- Add a new customer-facing layer for product browsing, ordering, and communication

\- Complete data isolation between factory tenants

\- Commercial platform for profit with my own branding

\*\*TARGET ARCHITECTURE:\*\*

\*\*FACTORY SIDE (Leverage Existing ERPNext):\*\*

\- Keep all existing modules that factories need (Manufacturing, Stock, Accounting, Projects, etc.)

\- Enhance user roles: Factory Admin (full access) and Factory Staff (limited access)

\- Add customer relationship management capabilities

\- Add customer account creation and management features

\- Extend product catalog for customer visibility

\- Enhance order management for customer interaction

\- Add production tracking with customer notifications

\*\*CUSTOMER SIDE (New Layer to Build):\*\*

\- Flutter mobile app AND web interface for customers

\- Customer login with factory-provided credentials

\- Multi-factory support (customers can access multiple factories they have relationships with)

\- Product catalog browsing with search and filtering

\- Order placement and tracking system

\- Real-time communication with factory staff

\- Quote request and management system

\- Multi-language support (English, Chinese, Arabic, Spanish, French)

\*\*KEY REQUIREMENTS TO IMPLEMENT:\*\*

\*\*Multi-Tenant Architecture:\*\*

\- Transform ERPNext to support multiple factory tenants with complete data isolation

\- Implement tenant-based routing and subdomain support

\- Each factory gets their own ERPNext instance with full functionality

\- Add Platform Super Admin role for managing all factory tenants

\*\*Customer Integration Layer:\*\*

\- Extend existing ERPNext Customer module for external customer relationships

\- Build RESTful APIs for mobile/web customer applications

\- Implement customer authentication and authorization system

\- Create customer-factory relationship management

\- Add customer-facing product catalog with rich media support

\*\*Enhanced Communication System:\*\*

\- Extend existing ERPNext communication features for factory-customer interaction

\- Implement real-time messaging with WebSocket support

\- Add file sharing and media attachments

\- Build notification system (in-app, email, push notifications)

\- Create inquiry and quote management workflows

\*\*Production Transparency:\*\*

\- Leverage existing Manufacturing module and extend it for customer visibility

\- Add production milestone tracking with customer notifications

\- Implement progress photo sharing and quality checkpoint documentation

\- Create estimated delivery tracking and updates

\- Build customer-facing order status dashboard

\*\*Payment Integration (Tracking Only):\*\*

\- Extend existing Accounts module for payment milestone tracking

\- Add invoice generation with detailed payment terms

\- Implement payment status tracking (no actual payment processing)

\- Create payment reminder and overdue management system

\- Support multi-currency display and international invoicing

\*\*Mobile and Web Experience:\*\*

\- Build Flutter mobile application with full customer functionality

\- Create responsive web interface for customers

\- Implement offline capability and data synchronization

\- Add push notification support for mobile app

\- Ensure cross-platform consistency and performance

\*\*DEVELOPMENT APPROACH:\*\*

Please analyze the current ERPNext structure and:

1\. \*\*Assess existing modules\*\* - Determine which ones to leverage, extend, or enhance

2\. \*\*Identify gaps\*\* - What customer-facing features need to be built from scratch

3\. \*\*Design multi-tenant architecture\*\* - How to transform single-company ERP to multi-tenant SaaS

4\. \*\*Plan API layer\*\* - What APIs need to be created for mobile/web customer applications

5\. \*\*Database schema extensions\*\* - What new tables and relationships are needed

6\. \*\*User experience design\*\* - How to create seamless factory-customer interaction

\*\*TECHNICAL DECISIONS I NEED YOUR EXPERTISE ON:\*\*

\- How to implement multi-tenancy while preserving all existing ERPNext functionality

\- Which existing modules can be extended vs which need custom development

\- Best approach for customer authentication and factory relationship management

\- Database architecture for multi-tenant data isolation

\- API design for mobile application performance

\- Real-time communication implementation strategy

\- Production tracking integration with existing Manufacturing module

\*\*CONSTRAINTS:\*\*

\- Maintain all existing ERPNext manufacturing and business functionality

\- Ensure complete data isolation between factory tenants

\- Create scalable architecture for thousands of factories and customers

\- Build enterprise-grade security and performance

\- Support international markets with multi-language capabilities

\*\*DELIVERABLE:\*\*

Provide a comprehensive technical roadmap showing:

1\. What existing ERPNext components to leverage and how to extend them

2\. What new components need to be built

3\. Database schema modifications and additions

4\. API architecture for customer applications

5\. Step-by-step implementation plan

6\. Technology stack recommendations for mobile/web customer interfaces

Please make professional architectural decisions based on ERPNext's current capabilities and recommend the most efficient way to achieve this transformation while maintaining code quality and system performance.