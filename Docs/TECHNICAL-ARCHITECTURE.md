# FC-CHINA Technical Architecture

## System Overview

FC-CHINA transforms ERPNext from a single-company ERP into a multi-tenant B2B marketplace while preserving all existing manufacturing capabilities. The architecture leverages ERPNext's robust foundation and extends it with customer-facing features and multi-tenant isolation.

## Architecture Principles

### 1. Preserve ERPNext Core
- Maintain all existing manufacturing, inventory, and accounting functionality
- Leverage ERPNext's Company model for tenant isolation
- Extend rather than replace existing modules
- Ensure compatibility with ERPNext updates

### 2. Multi-Tenant Isolation
- Complete data separation between factory tenants
- Tenant-scoped user authentication and authorization
- Isolated file storage and media assets
- Separate subdomain routing per tenant

### 3. Customer-Centric Design
- Mobile-first API design
- Real-time communication capabilities
- Production transparency and tracking
- Multi-language and multi-currency support

## System Components

### Core Platform Layer

#### ERPNext Foundation
```
erpnext/
├── manufacturing/          # Extended for customer visibility
├── stock/                 # Inventory management (preserved)
├── accounts/              # Financial management (preserved)
├── selling/               # Extended customer module
├── buying/                # Procurement (preserved)
├── setup/                 # Extended for tenant management
└── fc_china/              # New custom app for marketplace features
```

#### FC-China Custom App Structure
```
fc_china/
├── tenant_management/     # Multi-tenant infrastructure
├── customer_portal/       # Customer-facing features
├── communication/         # Real-time messaging
├── production_tracking/   # Manufacturing transparency
├── mobile_api/           # Mobile-optimized APIs
└── integrations/         # Third-party integrations
```

### Database Architecture

#### Tenant Isolation Strategy
**Approach**: Leverage ERPNext's existing Company-based data separation

```sql
-- Each factory becomes a Company with complete data isolation
-- All ERPNext tables already include company field for data separation

-- Example: Stock Ledger Entry with company isolation
CREATE TABLE `tabStock Ledger Entry` (
    `name` varchar(140) NOT NULL,
    `company` varchar(140) NOT NULL,  -- Tenant isolation field
    `item_code` varchar(140) NOT NULL,
    `warehouse` varchar(140) NOT NULL,
    -- ... other fields
    INDEX `company_item_warehouse` (`company`, `item_code`, `warehouse`)
);
```

#### New Tables for FC-China Features
```sql
-- Tenant Management
CREATE TABLE `tabFC Tenant` (
    `name` varchar(140) NOT NULL PRIMARY KEY,
    `company` varchar(140) NOT NULL,
    `subdomain` varchar(100) UNIQUE NOT NULL,
    `factory_name` varchar(200) NOT NULL,
    `status` varchar(20) DEFAULT 'Active',
    `created_date` datetime NOT NULL,
    `subscription_plan` varchar(50),
    FOREIGN KEY (`company`) REFERENCES `tabCompany`(`name`)
);

-- Customer-Factory Relationships
CREATE TABLE `tabCustomer Factory Access` (
    `name` varchar(140) NOT NULL PRIMARY KEY,
    `customer` varchar(140) NOT NULL,
    `factory_company` varchar(140) NOT NULL,
    `access_level` varchar(50) DEFAULT 'Standard',
    `granted_date` datetime NOT NULL,
    `status` varchar(20) DEFAULT 'Active',
    FOREIGN KEY (`customer`) REFERENCES `tabCustomer`(`name`),
    FOREIGN KEY (`factory_company`) REFERENCES `tabCompany`(`name`)
);

-- Production Tracking for Customers
CREATE TABLE `tabProduction Milestone` (
    `name` varchar(140) NOT NULL PRIMARY KEY,
    `work_order` varchar(140) NOT NULL,
    `company` varchar(140) NOT NULL,
    `milestone_name` varchar(200) NOT NULL,
    `description` text,
    `target_date` date,
    `completion_date` datetime,
    `status` varchar(20) DEFAULT 'Pending',
    `customer_visible` tinyint(1) DEFAULT 1,
    `photos` text,  -- JSON array of photo URLs
    FOREIGN KEY (`work_order`) REFERENCES `tabWork Order`(`name`),
    FOREIGN KEY (`company`) REFERENCES `tabCompany`(`name`)
);
```

### API Architecture

#### RESTful API Design
**Base URL Structure**: `https://{tenant}.fc-china.com/api/v1/`

#### Authentication Flow
```python
# JWT-based authentication with tenant context
class FCChinaAuth:
    def authenticate_customer(self, email, password, tenant_id):
        """
        Authenticate customer against specific tenant
        Returns JWT token with tenant and customer context
        """
        customer = self.validate_customer_credentials(email, password, tenant_id)
        if customer:
            token_payload = {
                'user_id': customer.name,
                'tenant_id': tenant_id,
                'user_type': 'customer',
                'permissions': self.get_customer_permissions(customer, tenant_id)
            }
            return jwt.encode(token_payload, secret_key, algorithm='HS256')
        return None
```

#### API Endpoint Categories

**1. Tenant Management APIs**
```
GET    /api/v1/tenants                    # List all tenants (Super Admin)
POST   /api/v1/tenants                    # Create new tenant
GET    /api/v1/tenants/{id}               # Get tenant details
PUT    /api/v1/tenants/{id}               # Update tenant
DELETE /api/v1/tenants/{id}               # Delete tenant
```

**2. Customer APIs**
```
POST   /api/v1/auth/customer/login        # Customer login
POST   /api/v1/auth/customer/register     # Customer registration
GET    /api/v1/customer/profile           # Get customer profile
PUT    /api/v1/customer/profile           # Update customer profile
GET    /api/v1/customer/factories         # List accessible factories
```

**3. Product Catalog APIs**
```
GET    /api/v1/products                   # List products with filters
GET    /api/v1/products/{id}              # Get product details
GET    /api/v1/products/search            # Search products
GET    /api/v1/products/categories        # List product categories
```

**4. Order Management APIs**
```
POST   /api/v1/orders                     # Create new order
GET    /api/v1/orders                     # List customer orders
GET    /api/v1/orders/{id}                # Get order details
PUT    /api/v1/orders/{id}                # Update order
GET    /api/v1/orders/{id}/tracking       # Get production tracking
```

### Real-time Communication

#### WebSocket Architecture
```python
# WebSocket connection management
class FCChinaWebSocket:
    def __init__(self):
        self.connections = {}  # tenant_id -> {customer_id: connection}
    
    def handle_customer_connection(self, websocket, tenant_id, customer_id):
        """Handle customer WebSocket connection"""
        self.connections.setdefault(tenant_id, {})[customer_id] = websocket
        
    def broadcast_to_customer(self, tenant_id, customer_id, message):
        """Send message to specific customer"""
        if tenant_id in self.connections and customer_id in self.connections[tenant_id]:
            connection = self.connections[tenant_id][customer_id]
            connection.send(json.dumps(message))
```

#### Message Types
```json
{
  "type": "production_update",
  "data": {
    "work_order": "WO-2024-001",
    "milestone": "Quality Check Complete",
    "completion_percentage": 75,
    "estimated_delivery": "2024-02-15",
    "photos": ["url1", "url2"]
  }
}

{
  "type": "chat_message",
  "data": {
    "from": "factory_user_id",
    "to": "customer_id",
    "message": "Your order is progressing well",
    "timestamp": "2024-01-15T10:30:00Z",
    "attachments": []
  }
}
```

### File Storage Architecture

#### Multi-Tenant File Organization
```
/files/
├── tenants/
│   ├── {tenant_id}/
│   │   ├── products/          # Product images and specifications
│   │   ├── production/        # Production photos and documents
│   │   ├── communications/    # Chat attachments
│   │   └── documents/         # Quotes, invoices, contracts
│   └── shared/
│       ├── templates/         # Shared document templates
│       └── assets/           # Platform assets
```

#### File Access Control
```python
class FCChinaFileAccess:
    def check_file_access(self, user, tenant_id, file_path):
        """Verify user has access to file within tenant context"""
        if not self.user_belongs_to_tenant(user, tenant_id):
            return False
        
        if file_path.startswith(f'/tenants/{tenant_id}/'):
            return self.check_specific_file_permissions(user, file_path)
        
        return False
```

### Caching Strategy

#### Multi-Level Caching
```python
# Redis-based caching with tenant isolation
class FCChinaCache:
    def __init__(self):
        self.redis_client = redis.Redis()
    
    def get_tenant_cache_key(self, tenant_id, key):
        return f"tenant:{tenant_id}:{key}"
    
    def cache_product_catalog(self, tenant_id, catalog_data):
        cache_key = self.get_tenant_cache_key(tenant_id, "product_catalog")
        self.redis_client.setex(cache_key, 3600, json.dumps(catalog_data))
```

### Security Architecture

#### Multi-Tenant Security Layers

**1. Network Level**
- Subdomain-based tenant routing
- SSL/TLS encryption for all communications
- DDoS protection and rate limiting

**2. Application Level**
- JWT-based authentication with tenant context
- Role-based access control (RBAC)
- API rate limiting per tenant

**3. Data Level**
- Company-based data isolation in database
- Encrypted sensitive data fields
- Audit logging for all data access

#### Security Implementation
```python
class FCChinaSecurity:
    def validate_tenant_access(self, user_token, tenant_id):
        """Validate user has access to specific tenant"""
        try:
            payload = jwt.decode(user_token, secret_key, algorithms=['HS256'])
            return payload.get('tenant_id') == tenant_id
        except jwt.InvalidTokenError:
            return False
    
    def audit_log(self, user_id, tenant_id, action, resource):
        """Log all user actions for security audit"""
        log_entry = {
            'timestamp': datetime.utcnow(),
            'user_id': user_id,
            'tenant_id': tenant_id,
            'action': action,
            'resource': resource,
            'ip_address': request.remote_addr
        }
        self.write_audit_log(log_entry)
```

## Performance Considerations

### Database Optimization
- Proper indexing on company field for all tables
- Query optimization for multi-tenant scenarios
- Connection pooling and query caching
- Read replicas for customer-facing queries

### API Performance
- Response caching for frequently accessed data
- Pagination for large datasets
- Asynchronous processing for heavy operations
- CDN for static assets and media files

### Scalability Patterns
- Horizontal scaling with load balancers
- Microservices architecture for high-load components
- Auto-scaling based on tenant usage
- Database sharding strategies for large-scale deployment

## Technology Stack

### Backend
- **Framework**: Frappe Framework (Python)
- **Database**: MariaDB with multi-tenant optimization
- **Cache**: Redis for session and data caching
- **Queue**: Celery for background job processing
- **Search**: Elasticsearch for product search

### Frontend
- **Mobile**: Flutter (iOS/Android)
- **Web**: Vue.js with responsive design
- **State Management**: Vuex/Redux
- **UI Components**: Custom component library

### Infrastructure
- **Deployment**: Docker containers with Kubernetes
- **Load Balancer**: Nginx with tenant routing
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **CDN**: CloudFlare for global performance

## Integration Points

### ERPNext Module Extensions
- **Manufacturing Module**: Extended Work Order and Job Card for customer visibility
- **Customer Module**: Enhanced with factory relationships and portal access
- **Communication Module**: Integrated with real-time messaging system
- **Stock Module**: Connected to production tracking and delivery estimates

### External Integrations
- **Payment Gateways**: Integration points for payment processing
- **Shipping APIs**: Integration with logistics providers
- **SMS/Email Services**: Notification delivery systems
- **Cloud Storage**: S3-compatible storage for files and media

## Cross-References

- [Multi-Tenant Design](./MULTI-TENANT-DESIGN.md) - Detailed multi-tenancy implementation
- [API Specifications](./API-SPECIFICATIONS.md) - Complete API documentation
- [Customer Integration](./CUSTOMER-INTEGRATION.md) - Customer-facing features
- [Manufacturing Extensions](./MANUFACTURING-EXTENSIONS.md) - Production transparency
- [Deployment Guide](./DEPLOYMENT-GUIDE.md) - Infrastructure and deployment
- [Development Standards](./DEVELOPMENT-STANDARDS.md) - Coding and security standards
