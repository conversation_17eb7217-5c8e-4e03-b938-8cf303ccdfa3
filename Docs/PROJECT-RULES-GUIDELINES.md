# FC-CHINA Project Rules and Guidelines

## Overview

This document establishes the fundamental rules, guidelines, and protocols for the FC-CHINA B2B marketplace project. These guidelines ensure we maintain ERPNext compatibility, achieve multi-tenant isolation, and deliver a production-ready marketplace while preserving all manufacturing capabilities.

## Project Vision Statement

**Primary Goal**: Transform ERPNext into a multi-tenant B2B marketplace that connects Chinese factories operating in China with global customers, maintaining full ERPNext manufacturing capabilities while adding modern customer-facing features.

**Core Principles**:
- Preserve all existing ERPNext functionality
- Extend, don't replace, ERPNext's core modules
- Maintain backward compatibility with ERPNext updates
- Ensure complete tenant data isolation
- Deliver production-ready, scalable solution
- **NO payment processing** - only payment reference tracking
- **China-focused** design for Chinese factories and business practices

## Development Rules

### 1. ERPNext Compatibility Rules

#### **RULE 1.1: No Core Module Modification**
- **NEVER** modify existing ERPNext core modules directly
- **ALWAYS** create custom apps that extend ERPNext functionality
- **ALWAYS** use Frappe's customization framework (custom fields, custom scripts, hooks)

```python
# ✅ CORRECT: Extend through custom app
# fc_china/hooks.py
doc_events = {
    "Sales Order": {
        "on_submit": "fc_china.customer_portal.api.notify_customer_order_confirmed"
    }
}

# ❌ WRONG: Modifying core ERPNext files
# erpnext/selling/doctype/sales_order/sales_order.py - DON'T TOUCH
```

#### **RULE 1.2: Use ERPNext's Extension Mechanisms**
- Custom Fields for additional data requirements
- Custom Scripts for client-side behavior
- Server Scripts for server-side logic
- Hooks for event handling
- Custom DocTypes for new functionality

#### **RULE 1.3: Maintain Update Compatibility**
- All customizations must survive ERPNext version updates
- Use version-specific compatibility checks when necessary
- Document any ERPNext version dependencies

### 2. Multi-Tenant Isolation Rules

#### **RULE 2.1: Company-Based Isolation**
- **ALWAYS** use ERPNext's Company field for tenant separation
- **NEVER** create custom tenant isolation mechanisms
- **ALWAYS** validate company access in all API endpoints

```python
# ✅ CORRECT: Company-based filtering
def get_customer_orders(customer_id):
    company = get_user_company()  # Get user's company context
    return frappe.get_all('Sales Order', 
        filters={'customer': customer_id, 'company': company})

# ❌ WRONG: Custom tenant field
def get_customer_orders(customer_id, tenant_id):
    return frappe.get_all('Sales Order', 
        filters={'customer': customer_id, 'custom_tenant': tenant_id})
```

#### **RULE 2.2: Data Access Validation**
- **ALWAYS** validate user's company access before data operations
- **NEVER** allow cross-company data access without explicit permission
- **ALWAYS** include company filters in database queries

#### **RULE 2.3: Session Management**
- **ALWAYS** include company context in user sessions
- **ALWAYS** validate company context on API requests
- **NEVER** allow company switching without proper authentication

### 3. Security Rules

#### **RULE 3.1: Input Validation**
- **ALWAYS** validate and sanitize all user inputs
- **ALWAYS** use parameterized queries for database operations
- **NEVER** trust client-side validation alone

#### **RULE 3.2: Authentication & Authorization**
- **ALWAYS** use JWT tokens for API authentication
- **ALWAYS** validate user permissions for each operation
- **ALWAYS** implement rate limiting on public APIs

#### **RULE 3.3: Data Protection**
- **ALWAYS** encrypt sensitive data at rest
- **ALWAYS** use HTTPS for all communications
- **NEVER** log sensitive information (passwords, tokens, personal data)

### 4. Code Quality Rules

#### **RULE 4.1: Documentation Requirements**
- **ALWAYS** document all public functions and classes
- **ALWAYS** include code examples in API documentation
- **ALWAYS** update documentation when changing functionality
- **ALWAYS** provide Chinese language documentation for factory users

#### **RULE 4.2: Testing Requirements**
- **ALWAYS** write unit tests for new functionality
- **ALWAYS** write integration tests for API endpoints
- **ALWAYS** achieve minimum 80% code coverage
- **ALWAYS** test with China Standard Time (CST) timezone

#### **RULE 4.3: Error Handling**
- **ALWAYS** implement comprehensive error handling
- **ALWAYS** log errors with appropriate context
- **NEVER** expose internal system details in error messages
- **ALWAYS** provide error messages in Chinese for factory users

#### **RULE 4.4: Payment Processing Restrictions**
- **NEVER** implement payment transaction processing
- **ALWAYS** only track payment reference numbers and status
- **NEVER** store sensitive payment information (card numbers, bank details)
- **ALWAYS** integrate with Chinese payment systems for reference tracking only

## Decision-Making Guidelines

### Technical Decision Framework

#### **GUIDELINE 1: ERPNext-First Approach**
When facing technical decisions, always ask:
1. Does ERPNext already provide this functionality?
2. Can we extend existing ERPNext features instead of building new ones?
3. Will this decision maintain compatibility with ERPNext updates?

#### **GUIDELINE 2: Multi-Tenant Considerations**
For every feature decision, evaluate:
1. How does this affect tenant data isolation?
2. Will this scale to 1000+ tenants?
3. Does this maintain performance across tenants?

#### **GUIDELINE 3: Customer Experience Priority**
When balancing technical approaches:
1. Prioritize customer-facing features over internal optimizations
2. Ensure mobile-first responsive design
3. Maintain sub-2-second API response times

### Architecture Decision Process

#### **STEP 1: Research Existing Solutions**
- Check ERPNext documentation and source code
- Review our project documentation for established patterns
- Consult with technical architect (me) before major decisions

#### **STEP 2: Evaluate Against Project Goals**
- Does this align with B2B marketplace vision?
- Does this preserve manufacturing capabilities?
- Does this support multi-tenant architecture?

#### **STEP 3: Document and Validate**
- Document the decision and rationale
- Update relevant project documentation
- Get approval for significant architectural changes

## Communication Protocols

### Development Collaboration

#### **PROTOCOL 1: Implementation Planning**
- **ALWAYS** break down work into 2-week sprints
- **ALWAYS** create detailed task breakdowns before starting
- **ALWAYS** estimate effort and identify dependencies

#### **PROTOCOL 2: Progress Reporting**
- **Weekly** progress updates with completed tasks
- **Immediate** notification of blockers or issues
- **Daily** commits with descriptive messages

#### **PROTOCOL 3: Technical Discussions**
- **ALWAYS** reference existing documentation in discussions
- **ALWAYS** provide code examples when discussing solutions
- **ALWAYS** consider impact on other system components

### Code Review Process

#### **PROTOCOL 4: Review Requirements**
- **ALL** code changes require review before merging
- **ALL** API changes require architecture review
- **ALL** database schema changes require approval

#### **PROTOCOL 5: Review Criteria**
- Code follows established standards (see DEVELOPMENT-STANDARDS.md)
- Tests are included and passing
- Documentation is updated
- Security considerations are addressed
- Multi-tenant isolation is maintained

## Quality Standards

### Mandatory Practices

#### **STANDARD 1: Code Quality**
- Follow PEP 8 for Python code
- Use ESLint/Prettier for JavaScript code
- Maintain consistent naming conventions
- Keep functions under 50 lines when possible

#### **STANDARD 2: Testing Requirements**
- Unit tests for all business logic
- Integration tests for all API endpoints
- Performance tests for critical paths
- Security tests for authentication flows

#### **STANDARD 3: Documentation Standards**
- API documentation with examples
- Code comments for complex logic
- README files for each module
- Architecture decision records (ADRs)

### Performance Standards

#### **STANDARD 4: Response Time Requirements**
- API endpoints: < 2 seconds (95th percentile)
- Database queries: < 500ms (average)
- Page load times: < 3 seconds (initial load)
- Mobile app responsiveness: < 1 second (UI interactions)

#### **STANDARD 5: Scalability Requirements**
- Support 1000+ concurrent users
- Handle 10,000+ API requests per minute
- Scale to 500+ factory tenants
- Support 50,000+ customer accounts

## Project Boundaries

### What We WILL Modify

#### **ALLOWED MODIFICATIONS**
- Add custom fields to existing DocTypes
- Create new custom DocTypes for marketplace features
- Add custom scripts and hooks
- Create new API endpoints
- Add custom reports and dashboards
- Extend existing workflows

### What We WILL NOT Modify

#### **FORBIDDEN MODIFICATIONS**
- Core ERPNext module files (erpnext/*)
- Frappe framework files (frappe/*)
- Database schema of existing DocTypes
- Core business logic of manufacturing modules
- Standard ERPNext workflows and permissions

#### **EXCEPTION PROCESS**
If core modifications become necessary:
1. Document the specific requirement
2. Explore all alternative approaches
3. Get explicit approval from project stakeholder
4. Plan for update compatibility
5. Document the modification thoroughly

### Integration Boundaries

#### **EXTERNAL INTEGRATIONS**
- **Payment Reference Systems** (Alipay, WeChat Pay, Bank transfers) - **REFERENCE TRACKING ONLY, NO TRANSACTION PROCESSING**
- Shipping providers (China Post, SF Express, local Chinese carriers)
- SMS/Email services (Chinese providers, Twilio, SendGrid)
- File storage (Alibaba Cloud OSS, AWS S3, local storage)
- Chinese business compliance systems

#### **INTERNAL INTEGRATIONS**
- ERPNext manufacturing modules
- ERPNext accounting modules
- ERPNext CRM modules
- Frappe framework APIs

## Success Criteria

### Phase 1 Success Metrics (Foundation & Multi-Tenancy)

#### **TECHNICAL METRICS**
- [ ] Multi-tenant architecture implemented with complete data isolation
- [ ] 10 factory tenants successfully onboarded
- [ ] All existing ERPNext functionality preserved
- [ ] API response times under 2 seconds
- [ ] 95%+ uptime achieved

#### **FUNCTIONAL METRICS**
- [ ] Factory admin can create and manage company profile
- [ ] Tenant provisioning system operational
- [ ] Basic customer registration and authentication working
- [ ] Manufacturing data visible to appropriate customers
- [ ] Mobile API endpoints functional

### Overall Project Success Criteria

#### **BUSINESS METRICS**
- [ ] 100+ active factory tenants
- [ ] 1,000+ registered customers
- [ ] 10,000+ monthly transactions
- [ ] 99.9% system uptime
- [ ] Sub-2-second average response times

#### **TECHNICAL METRICS**
- [ ] Zero data breaches or security incidents
- [ ] 100% ERPNext update compatibility maintained
- [ ] 90%+ code coverage in tests
- [ ] All documentation up-to-date and accurate
- [ ] Successful production deployment

## Escalation Procedures

### Technical Issues
1. **Level 1**: Developer attempts resolution (max 4 hours)
2. **Level 2**: Team discussion and collaborative debugging
3. **Level 3**: Escalate to technical architect (me) with detailed context
4. **Level 4**: Consider external ERPNext community consultation

### Project Scope Changes
1. Document the proposed change and rationale
2. Assess impact on timeline and resources
3. Review against project boundaries and rules
4. Get stakeholder approval before implementation

### Quality Issues
1. Immediate halt of affected development
2. Root cause analysis and documentation
3. Implementation of preventive measures
4. Update of relevant guidelines and standards

## Continuous Improvement

### Monthly Reviews
- Review adherence to these guidelines
- Update rules based on lessons learned
- Assess project progress against success criteria
- Identify process improvements

### Documentation Updates
- Keep all project documentation current
- Update guidelines based on new ERPNext versions
- Incorporate feedback from development team
- Maintain alignment with project goals

## Cross-References

- [FC-CHINA Roadmap](./FC-CHINA-ROADMAP.md) - Implementation phases and timeline
- [Technical Architecture](./TECHNICAL-ARCHITECTURE.md) - System design principles
- [Multi-Tenant Design](./MULTI-TENANT-DESIGN.md) - Tenant isolation strategies
- [Development Standards](./DEVELOPMENT-STANDARDS.md) - Detailed coding standards
- [API Specifications](./API-SPECIFICATIONS.md) - API design guidelines
- [Deployment Guide](./DEPLOYMENT-GUIDE.md) - Production deployment rules

---

**Document Version**: 1.0  
**Last Updated**: 2025-07-28  
**Next Review**: 2025-08-28  

*These guidelines are living documents that will evolve with the project. All team members are responsible for following these rules and suggesting improvements.*
