# FC-CHINA Development Standards

## Overview

This document establishes comprehensive development standards, coding guidelines, security practices, and quality assurance procedures for the FC-CHINA B2B marketplace project. All team members must adhere to these standards to ensure code quality, security, and maintainability.

## Code Organization

### Project Structure
```
fc_china/
├── __init__.py
├── hooks.py                    # Frappe hooks and configurations
├── modules.txt                 # Module definitions
├── patches.txt                 # Database patches
├── config/
│   ├── desktop.py             # Desktop configuration
│   └── docs.py                # Documentation configuration
├── tenant_management/         # Multi-tenant infrastructure
│   ├── doctype/
│   ├── api.py
│   └── utils.py
├── customer_portal/           # Customer-facing features
│   ├── doctype/
│   ├── api.py
│   ├── auth.py
│   └── dashboard.py
├── manufacturing_extensions/   # Production tracking
│   ├── doctype/
│   ├── api.py
│   └── notifications.py
├── communication/             # Real-time messaging
│   ├── doctype/
│   ├── websocket.py
│   └── notifications.py
├── mobile_api/               # Mobile-optimized APIs
│   ├── v1/
│   └── utils.py
├── integrations/             # Third-party integrations
│   ├── payment/
│   ├── shipping/
│   └── sms/
├── public/                   # Static assets
│   ├── css/
│   ├── js/
│   └── images/
├── templates/                # Jinja2 templates
│   ├── emails/
│   └── web/
└── tests/                    # Test files
    ├── unit/
    ├── integration/
    └── fixtures/
```

## Coding Standards

### Python Code Style

#### PEP 8 Compliance
All Python code must follow PEP 8 guidelines with these specific requirements:

```python
# Good: Clear, descriptive naming
class CustomerFactoryAccess(Document):
    def validate_access_permissions(self):
        """Validate customer has proper access to factory resources"""
        if not self.customer or not self.factory_company:
            frappe.throw(_("Customer and Factory Company are required"))
        
        # Check for existing access
        existing_access = frappe.db.exists('Customer Factory Access', {
            'customer': self.customer,
            'factory_company': self.factory_company,
            'status': 'active'
        })
        
        if existing_access and existing_access != self.name:
            frappe.throw(_("Customer already has active access to this factory"))

# Bad: Unclear naming and poor structure
class CFA(Document):
    def val(self):
        if not self.c or not self.fc:
            frappe.throw("Required")
        ea = frappe.db.exists('Customer Factory Access', {'customer': self.c, 'factory_company': self.fc, 'status': 'active'})
        if ea and ea != self.name:
            frappe.throw("Exists")
```

#### Import Organization
```python
# Standard library imports
import json
import datetime
from typing import Dict, List, Optional, Union

# Third-party imports
import redis
import jwt
from celery import Celery

# Frappe imports
import frappe
from frappe import _
from frappe.model.document import Document
from frappe.utils import now, cint, flt, getdate

# Local application imports
from fc_china.tenant_management.utils import get_tenant_company
from fc_china.customer_portal.auth import validate_customer_access
```

#### Error Handling
```python
class TenantManager:
    def create_tenant(self, tenant_data: Dict) -> str:
        """Create new tenant with proper error handling"""
        
        try:
            # Validate input data
            self.validate_tenant_data(tenant_data)
            
            # Create tenant record
            tenant = frappe.get_doc({
                'doctype': 'FC Tenant',
                'tenant_id': tenant_data['tenant_id'],
                'company_name': tenant_data['company_name'],
                'subdomain': tenant_data['subdomain']
            })
            tenant.insert()
            
            # Create associated company
            company = self.create_tenant_company(tenant_data)
            tenant.company = company.name
            tenant.save()
            
            return tenant.name
            
        except frappe.ValidationError as e:
            frappe.log_error(f"Tenant validation failed: {str(e)}", "Tenant Creation")
            raise
        except Exception as e:
            frappe.log_error(f"Unexpected error in tenant creation: {str(e)}", "Tenant Creation")
            # Cleanup partial creation
            self.cleanup_failed_tenant_creation(tenant_data.get('tenant_id'))
            raise frappe.ValidationError(_("Failed to create tenant. Please try again."))
    
    def validate_tenant_data(self, data: Dict) -> None:
        """Validate tenant creation data"""
        required_fields = ['tenant_id', 'company_name', 'subdomain']
        
        for field in required_fields:
            if not data.get(field):
                raise frappe.ValidationError(_(f"{field} is required"))
        
        # Validate subdomain format
        if not self.is_valid_subdomain(data['subdomain']):
            raise frappe.ValidationError(_("Invalid subdomain format"))
        
        # Check for duplicates
        if frappe.db.exists('FC Tenant', {'subdomain': data['subdomain']}):
            raise frappe.ValidationError(_("Subdomain already exists"))
```

### JavaScript/TypeScript Standards

#### ES6+ Features
```javascript
// Good: Modern JavaScript with proper async/await
class CustomerAPI {
    constructor(baseURL, authToken) {
        this.baseURL = baseURL;
        this.authToken = authToken;
        this.httpClient = axios.create({
            baseURL: this.baseURL,
            headers: {
                'Authorization': `Bearer ${this.authToken}`,
                'Content-Type': 'application/json'
            }
        });
    }
    
    async getCustomerOrders(customerId, filters = {}) {
        try {
            const response = await this.httpClient.get(`/customers/${customerId}/orders`, {
                params: filters
            });
            
            return {
                success: true,
                data: response.data,
                pagination: response.data.pagination
            };
        } catch (error) {
            console.error('Failed to fetch customer orders:', error);
            return {
                success: false,
                error: error.response?.data?.message || 'Unknown error occurred'
            };
        }
    }
}

// Bad: Old JavaScript patterns
function getOrders(id, cb) {
    $.ajax({
        url: '/api/orders?customer=' + id,
        success: function(data) {
            cb(null, data);
        },
        error: function(err) {
            cb(err);
        }
    });
}
```

#### Vue.js Component Standards
```vue
<template>
  <div class="customer-dashboard">
    <div class="dashboard-header">
      <h1>{{ $t('dashboard.welcome', { name: customer.name }) }}</h1>
      <div class="factory-selector">
        <select v-model="selectedFactory" @change="onFactoryChange">
          <option v-for="factory in customerFactories" 
                  :key="factory.tenant_id" 
                  :value="factory.tenant_id">
            {{ factory.factory_name }}
          </option>
        </select>
      </div>
    </div>
    
    <div class="dashboard-content">
      <OrderSummary :orders="orders" :loading="ordersLoading" />
      <ProductionTracking :work-orders="workOrders" />
      <RecentMessages :messages="messages" />
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import OrderSummary from '@/components/OrderSummary.vue'
import ProductionTracking from '@/components/ProductionTracking.vue'
import RecentMessages from '@/components/RecentMessages.vue'

export default {
  name: 'CustomerDashboard',
  
  components: {
    OrderSummary,
    ProductionTracking,
    RecentMessages
  },
  
  data() {
    return {
      selectedFactory: null,
      ordersLoading: false
    }
  },
  
  computed: {
    ...mapState('customer', ['customer', 'customerFactories']),
    ...mapState('orders', ['orders']),
    ...mapState('production', ['workOrders']),
    ...mapState('messages', ['messages'])
  },
  
  methods: {
    ...mapActions('orders', ['fetchCustomerOrders']),
    ...mapActions('production', ['fetchWorkOrders']),
    ...mapActions('messages', ['fetchRecentMessages']),
    
    async onFactoryChange() {
      if (!this.selectedFactory) return
      
      this.ordersLoading = true
      
      try {
        await Promise.all([
          this.fetchCustomerOrders({ tenantId: this.selectedFactory }),
          this.fetchWorkOrders({ tenantId: this.selectedFactory }),
          this.fetchRecentMessages({ tenantId: this.selectedFactory })
        ])
      } catch (error) {
        this.$toast.error(this.$t('errors.failed_to_load_data'))
      } finally {
        this.ordersLoading = false
      }
    }
  },
  
  async mounted() {
    if (this.customerFactories.length > 0) {
      this.selectedFactory = this.customerFactories[0].tenant_id
      await this.onFactoryChange()
    }
  }
}
</script>

<style scoped>
.customer-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.factory-selector select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 20px;
}

@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
}
</style>
```

## Security Standards

### Authentication and Authorization

#### JWT Token Management
```python
# fc_china/security/auth.py
import jwt
from datetime import datetime, timedelta
from typing import Dict, Optional

class FCChinaAuth:
    def __init__(self):
        self.secret_key = frappe.conf.get('jwt_secret_key')
        self.algorithm = 'HS256'
        self.access_token_expiry = timedelta(hours=24)
        self.refresh_token_expiry = timedelta(days=30)
    
    def generate_tokens(self, user_id: str, tenant_id: str, user_type: str) -> Dict[str, str]:
        """Generate access and refresh tokens"""
        
        now = datetime.utcnow()
        
        # Access token payload
        access_payload = {
            'user_id': user_id,
            'tenant_id': tenant_id,
            'user_type': user_type,
            'iat': now,
            'exp': now + self.access_token_expiry,
            'type': 'access'
        }
        
        # Refresh token payload
        refresh_payload = {
            'user_id': user_id,
            'tenant_id': tenant_id,
            'iat': now,
            'exp': now + self.refresh_token_expiry,
            'type': 'refresh'
        }
        
        access_token = jwt.encode(access_payload, self.secret_key, algorithm=self.algorithm)
        refresh_token = jwt.encode(refresh_payload, self.secret_key, algorithm=self.algorithm)
        
        # Store refresh token in database
        self.store_refresh_token(user_id, refresh_token)
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'expires_in': int(self.access_token_expiry.total_seconds())
        }
    
    def validate_token(self, token: str) -> Optional[Dict]:
        """Validate JWT token and return payload"""
        
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Additional validation
            if payload.get('type') != 'access':
                return None
            
            # Check if user is still active
            if not self.is_user_active(payload.get('user_id')):
                return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            frappe.log_error("JWT token expired", "Authentication")
            return None
        except jwt.InvalidTokenError:
            frappe.log_error("Invalid JWT token", "Authentication")
            return None
```

#### Input Validation and Sanitization
```python
# fc_china/security/validation.py
import re
from typing import Any, Dict, List
from frappe.utils import cstr, cint, flt

class InputValidator:
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_subdomain(subdomain: str) -> bool:
        """Validate subdomain format"""
        pattern = r'^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$'
        return bool(re.match(pattern, subdomain.lower()))
    
    @staticmethod
    def sanitize_html_input(html_content: str) -> str:
        """Sanitize HTML content to prevent XSS"""
        import bleach
        
        allowed_tags = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'a']
        allowed_attributes = {'a': ['href', 'title']}
        
        return bleach.clean(html_content, tags=allowed_tags, attributes=allowed_attributes)
    
    @staticmethod
    def validate_api_input(data: Dict, schema: Dict) -> Dict:
        """Validate API input against schema"""
        validated_data = {}
        errors = []
        
        for field, rules in schema.items():
            value = data.get(field)
            
            # Check required fields
            if rules.get('required') and not value:
                errors.append(f"{field} is required")
                continue
            
            # Skip validation for optional empty fields
            if not value and not rules.get('required'):
                continue
            
            # Type validation
            field_type = rules.get('type')
            if field_type == 'string':
                validated_data[field] = cstr(value)
            elif field_type == 'integer':
                validated_data[field] = cint(value)
            elif field_type == 'float':
                validated_data[field] = flt(value)
            elif field_type == 'email':
                if not InputValidator.validate_email(value):
                    errors.append(f"{field} must be a valid email")
                else:
                    validated_data[field] = cstr(value).lower()
            
            # Length validation
            if 'max_length' in rules and len(cstr(value)) > rules['max_length']:
                errors.append(f"{field} must be less than {rules['max_length']} characters")
        
        if errors:
            raise frappe.ValidationError('; '.join(errors))
        
        return validated_data
```

### SQL Injection Prevention
```python
# Always use parameterized queries
class TenantDataAccess:
    @staticmethod
    def get_customer_orders(customer_id: str, tenant_company: str, filters: Dict) -> List[Dict]:
        """Get customer orders with proper SQL injection prevention"""
        
        # Build WHERE conditions safely
        conditions = ["so.customer = %(customer)s", "so.company = %(company)s"]
        params = {
            'customer': customer_id,
            'company': tenant_company
        }
        
        # Add optional filters
        if filters.get('status'):
            conditions.append("so.status = %(status)s")
            params['status'] = filters['status']
        
        if filters.get('from_date'):
            conditions.append("so.transaction_date >= %(from_date)s")
            params['from_date'] = filters['from_date']
        
        if filters.get('to_date'):
            conditions.append("so.transaction_date <= %(to_date)s")
            params['to_date'] = filters['to_date']
        
        # Execute safe query
        query = f"""
            SELECT so.name, so.customer, so.transaction_date, so.status, 
                   so.grand_total, so.currency
            FROM `tabSales Order` so
            WHERE {' AND '.join(conditions)}
            ORDER BY so.transaction_date DESC
            LIMIT %(limit)s OFFSET %(offset)s
        """
        
        params.update({
            'limit': filters.get('limit', 20),
            'offset': filters.get('offset', 0)
        })
        
        return frappe.db.sql(query, params, as_dict=True)
```

## Testing Standards

### Unit Testing
```python
# fc_china/tests/unit/test_tenant_management.py
import unittest
import frappe
from frappe.test_runner import make_test_records

from fc_china.tenant_management.utils import TenantManager

class TestTenantManagement(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """Set up test data"""
        make_test_records("Company")
        make_test_records("User")
    
    def setUp(self):
        """Set up each test"""
        self.tenant_manager = TenantManager()
        self.test_tenant_data = {
            'tenant_id': 'test_factory_001',
            'company_name': 'Test Factory Ltd',
            'subdomain': 'test-factory',
            'factory_name': 'Test Factory',
            'admin_email': '<EMAIL>'
        }
    
    def test_create_tenant_success(self):
        """Test successful tenant creation"""
        tenant_name = self.tenant_manager.create_tenant(self.test_tenant_data)
        
        # Verify tenant was created
        self.assertTrue(frappe.db.exists('FC Tenant', tenant_name))
        
        # Verify associated company was created
        tenant = frappe.get_doc('FC Tenant', tenant_name)
        self.assertTrue(frappe.db.exists('Company', tenant.company))
        
        # Cleanup
        frappe.delete_doc('FC Tenant', tenant_name)
    
    def test_create_tenant_duplicate_subdomain(self):
        """Test tenant creation with duplicate subdomain"""
        # Create first tenant
        tenant_name = self.tenant_manager.create_tenant(self.test_tenant_data)
        
        # Try to create second tenant with same subdomain
        duplicate_data = self.test_tenant_data.copy()
        duplicate_data['tenant_id'] = 'test_factory_002'
        
        with self.assertRaises(frappe.ValidationError):
            self.tenant_manager.create_tenant(duplicate_data)
        
        # Cleanup
        frappe.delete_doc('FC Tenant', tenant_name)
    
    def test_validate_tenant_data_missing_fields(self):
        """Test validation with missing required fields"""
        incomplete_data = {'tenant_id': 'test_001'}
        
        with self.assertRaises(frappe.ValidationError):
            self.tenant_manager.validate_tenant_data(incomplete_data)
    
    def tearDown(self):
        """Clean up after each test"""
        # Remove any test tenants that might have been created
        test_tenants = frappe.get_all('FC Tenant', 
            filters={'tenant_id': ['like', 'test_%']})
        
        for tenant in test_tenants:
            frappe.delete_doc('FC Tenant', tenant.name)
```

### Integration Testing
```python
# fc_china/tests/integration/test_customer_api.py
import json
import unittest
import frappe
from frappe.test_runner import make_test_records

class TestCustomerAPI(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """Set up test environment"""
        make_test_records("Customer")
        make_test_records("Sales Order")
        
        # Create test tenant
        cls.test_tenant = frappe.get_doc({
            'doctype': 'FC Tenant',
            'tenant_id': 'test_api_factory',
            'company_name': 'API Test Factory',
            'subdomain': 'api-test-factory'
        })
        cls.test_tenant.insert()
    
    def setUp(self):
        """Set up each test"""
        self.client = frappe.test_client()
        
        # Login as test customer
        self.customer_token = self.get_customer_auth_token()
    
    def get_customer_auth_token(self):
        """Get authentication token for test customer"""
        response = self.client.post('/api/v1/auth/customer/login', 
            data=json.dumps({
                'email': '<EMAIL>',
                'password': 'test_password',
                'tenant_id': 'test_api_factory'
            }),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        return data['data']['access_token']
    
    def test_get_customer_orders(self):
        """Test customer orders API endpoint"""
        headers = {
            'Authorization': f'Bearer {self.customer_token}',
            'X-Tenant-ID': 'test_api_factory'
        }
        
        response = self.client.get('/api/v1/orders', headers=headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('orders', data['data'])
        self.assertIn('pagination', data['data'])
    
    def test_unauthorized_access(self):
        """Test API access without authentication"""
        response = self.client.get('/api/v1/orders')
        self.assertEqual(response.status_code, 401)
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test environment"""
        frappe.delete_doc('FC Tenant', cls.test_tenant.name)
```

### Performance Testing
```python
# fc_china/tests/performance/test_api_performance.py
import time
import unittest
import concurrent.futures
from statistics import mean, median

class TestAPIPerformance(unittest.TestCase):
    def setUp(self):
        self.base_url = 'https://test.fc-china.com/api/v1'
        self.auth_token = self.get_test_auth_token()
    
    def test_concurrent_api_requests(self):
        """Test API performance under concurrent load"""
        num_requests = 100
        num_threads = 10
        
        def make_request():
            start_time = time.time()
            response = self.client.get(f'{self.base_url}/orders',
                headers={'Authorization': f'Bearer {self.auth_token}'})
            end_time = time.time()
            
            return {
                'status_code': response.status_code,
                'response_time': end_time - start_time
            }
        
        # Execute concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(make_request) for _ in range(num_requests)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Analyze results
        response_times = [r['response_time'] for r in results]
        success_count = sum(1 for r in results if r['status_code'] == 200)
        
        # Assertions
        self.assertEqual(success_count, num_requests, "All requests should succeed")
        self.assertLess(mean(response_times), 2.0, "Average response time should be under 2 seconds")
        self.assertLess(median(response_times), 1.0, "Median response time should be under 1 second")
        self.assertLess(max(response_times), 5.0, "No request should take more than 5 seconds")
```

## Documentation Standards

### Code Documentation
```python
class CustomerPortalManager:
    """
    Manages customer portal functionality including authentication,
    data access, and communication features.
    
    This class provides a centralized interface for all customer-facing
    operations while maintaining proper tenant isolation and security.
    
    Attributes:
        tenant_id (str): The tenant identifier for multi-tenant operations
        cache_timeout (int): Cache timeout in seconds for customer data
    
    Example:
        >>> manager = CustomerPortalManager('factory_001')
        >>> customer_data = manager.get_customer_dashboard('CUST-001')
        >>> orders = manager.get_customer_orders('CUST-001', {'status': 'active'})
    """
    
    def __init__(self, tenant_id: str):
        """
        Initialize the customer portal manager.
        
        Args:
            tenant_id (str): The tenant identifier for this manager instance
            
        Raises:
            ValidationError: If tenant_id is invalid or tenant doesn't exist
        """
        self.tenant_id = tenant_id
        self.cache_timeout = 3600  # 1 hour
        self.tenant_company = self._get_tenant_company()
    
    def get_customer_orders(self, customer_id: str, filters: Dict = None) -> Dict:
        """
        Retrieve customer orders with optional filtering.
        
        This method returns paginated customer orders for the specified tenant,
        applying proper access controls and data isolation.
        
        Args:
            customer_id (str): The customer identifier
            filters (Dict, optional): Filter criteria including:
                - status (str): Order status filter
                - from_date (str): Start date filter (YYYY-MM-DD)
                - to_date (str): End date filter (YYYY-MM-DD)
                - page (int): Page number for pagination (default: 1)
                - limit (int): Items per page (default: 20, max: 100)
        
        Returns:
            Dict: Response containing:
                - orders (List[Dict]): List of order objects
                - pagination (Dict): Pagination information
                - total_count (int): Total number of orders
        
        Raises:
            PermissionError: If customer doesn't have access to this tenant
            ValidationError: If filters contain invalid values
            
        Example:
            >>> filters = {'status': 'active', 'page': 1, 'limit': 10}
            >>> result = manager.get_customer_orders('CUST-001', filters)
            >>> print(f"Found {len(result['orders'])} orders")
        """
        # Validate customer access
        if not self._validate_customer_access(customer_id):
            raise frappe.PermissionError("Customer doesn't have access to this tenant")
        
        # Apply default filters
        filters = filters or {}
        filters.setdefault('page', 1)
        filters.setdefault('limit', 20)
        
        # Validate filters
        self._validate_order_filters(filters)
        
        # Get orders from database
        orders = self._fetch_customer_orders(customer_id, filters)
        total_count = self._count_customer_orders(customer_id, filters)
        
        return {
            'orders': orders,
            'pagination': self._build_pagination(filters['page'], filters['limit'], total_count),
            'total_count': total_count
        }
```

### API Documentation
```python
@frappe.whitelist()
def get_production_status(order_id: str) -> Dict:
    """
    Get production status for a customer order.
    
    **Endpoint:** `GET /api/v1/orders/{order_id}/production`
    
    **Authentication:** Bearer token required
    
    **Parameters:**
    - order_id (str): Sales Order ID
    
    **Response:**
    ```json
    {
        "success": true,
        "data": {
            "order_id": "SO-2024-001",
            "production_items": [
                {
                    "work_order": "WO-2024-001",
                    "item": "ITEM-001",
                    "quantity": 100,
                    "status": "in_progress",
                    "progress_percentage": 65,
                    "milestones": [...],
                    "estimated_delivery": "2024-03-15"
                }
            ],
            "overall_progress": 65,
            "estimated_completion": "2024-03-15"
        }
    }
    ```
    
    **Error Responses:**
    - 401: Authentication required
    - 403: Access denied to this order
    - 404: Order not found
    - 500: Internal server error
    
    **Rate Limit:** 200 requests per minute
    """
    pass
```

## Quality Assurance

### Code Review Checklist
- [ ] Code follows established style guidelines
- [ ] All functions and classes have proper documentation
- [ ] Security best practices are implemented
- [ ] Input validation is comprehensive
- [ ] Error handling is appropriate
- [ ] Tests cover new functionality
- [ ] Performance implications are considered
- [ ] Multi-tenant isolation is maintained
- [ ] Database queries are optimized
- [ ] API responses follow standard format

### Pre-commit Hooks
```bash
#!/bin/bash
# .git/hooks/pre-commit

# Run code formatting
black fc_china/
isort fc_china/

# Run linting
flake8 fc_china/
pylint fc_china/

# Run security checks
bandit -r fc_china/

# Run tests
python -m pytest fc_china/tests/unit/

# Check for secrets
git-secrets --scan

echo "Pre-commit checks completed successfully"
```

### Continuous Integration
```yaml
# .github/workflows/ci.yml
name: FC-CHINA CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mariadb:
        image: mariadb:10.6
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test_fc_china
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7-alpine
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run linting
      run: |
        flake8 fc_china/
        pylint fc_china/
    
    - name: Run security checks
      run: bandit -r fc_china/
    
    - name: Run unit tests
      run: python -m pytest fc_china/tests/unit/ --cov=fc_china
    
    - name: Run integration tests
      run: python -m pytest fc_china/tests/integration/
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
```

## Cross-References

- [Technical Architecture](./TECHNICAL-ARCHITECTURE.md) - System architecture and design patterns
- [Multi-Tenant Design](./MULTI-TENANT-DESIGN.md) - Multi-tenancy implementation guidelines
- [API Specifications](./API-SPECIFICATIONS.md) - API design and documentation standards
- [Customer Integration](./CUSTOMER-INTEGRATION.md) - Customer-facing development guidelines
- [Manufacturing Extensions](./MANUFACTURING-EXTENSIONS.md) - Manufacturing module development
- [Deployment Guide](./DEPLOYMENT-GUIDE.md) - Production deployment and DevOps practices
