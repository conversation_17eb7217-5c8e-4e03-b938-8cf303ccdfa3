# Tests are skipped for these files but github doesn't allow "passing" hence this is required.

name: Skipped Tests

on:
  pull_request:
    paths:
      - "**.js"
      - "**.css"
      - "**.md"
      - "**.html"

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        container: [1, 2, 3, 4]

    name: Python Unit Tests

    steps:
      - name: Pass skipped tests unconditionally
        run: "echo Skipped"
