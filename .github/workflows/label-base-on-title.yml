name: "Auto-label PRs based on title"

on:
  pull_request_target:
    types: [opened, reopened]

jobs:
  add-label-if-prefix-matches:
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - name: Check PR title and add label if it matches prefixes
        uses: actions/github-script@v7
        continue-on-error: true
        with:
          script: |
            const title = context.payload.pull_request.title.toLowerCase();
            const prefixes = ['chore', 'ci', 'style', 'test', 'refactor'];
            
            // Check if the PR title starts with any of the prefixes
            if (prefixes.some(prefix => title.startsWith(prefix))) {
              await github.rest.issues.addLabels({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.payload.pull_request.number,
                labels: ['skip-release-notes']
              });
            }
