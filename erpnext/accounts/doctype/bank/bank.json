{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:bank_name", "creation": "2018-04-07 16:59:59.496668", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["bank_details_section", "bank_name", "swift_number", "column_break_1", "website", "address_and_contact", "address_html", "column_break_13", "contact_html", "data_import_configuration_section", "bank_transaction_mapping", "section_break_4", "plaid_access_token"], "fields": [{"fieldname": "bank_name", "fieldtype": "Data", "label": "Bank Name", "reqd": 1, "unique": 1}, {"fieldname": "bank_details_section", "fieldtype": "Section Break", "label": "Bank Details"}, {"allow_in_quick_entry": 1, "fieldname": "swift_number", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "SWIFT number", "unique": 1}, {"fieldname": "column_break_1", "fieldtype": "Column Break", "search_index": 1}, {"fieldname": "address_and_contact", "fieldtype": "Section Break", "label": "Address and Contact", "options": "fa fa-map-marker"}, {"fieldname": "address_html", "fieldtype": "HTML", "label": "Address HTML"}, {"fieldname": "website", "fieldtype": "Data", "label": "Website"}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}, {"fieldname": "contact_html", "fieldtype": "HTML", "label": "Contact HTML"}, {"collapsible": 1, "fieldname": "data_import_configuration_section", "fieldtype": "Section Break", "label": "Data Import Configuration"}, {"fieldname": "bank_transaction_mapping", "fieldtype": "Table", "label": "Bank Transaction Mapping", "options": "Bank Transaction Mapping"}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "plaid_access_token", "fieldtype": "Data", "hidden": 1, "label": "Plaid Access Token", "no_copy": 1, "read_only": 1}], "links": [], "modified": "2024-03-27 13:06:36.896195", "modified_by": "Administrator", "module": "Accounts", "name": "Bank", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}