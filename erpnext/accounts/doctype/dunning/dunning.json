{"actions": [], "allow_events_in_timeline": 1, "autoname": "naming_series:", "beta": 1, "creation": "2019-07-05 16:34:31.013238", "doctype": "DocType", "engine": "InnoDB", "field_order": ["naming_series", "customer", "customer_name", "column_break_3", "company", "posting_date", "posting_time", "status", "section_break_9", "currency", "column_break_11", "conversion_rate", "section_break_6", "dunning_type", "column_break_8", "rate_of_interest", "section_break_12", "overdue_payments", "section_break_28", "total_interest", "dunning_fee", "column_break_17", "dunning_amount", "base_dunning_amount", "section_break_32", "spacer", "column_break_33", "total_outstanding", "grand_total", "printing_settings_section", "language", "body_text", "column_break_22", "letter_head", "closing_text", "accounting_details_section", "income_account", "column_break_48", "cost_center", "amended_from", "address_and_contact_tab", "address_and_contact_section", "customer_address", "address_display", "column_break_vodj", "contact_person", "contact_display", "contact_mobile", "contact_email", "section_break_xban", "column_break_16", "company_address", "company_address_display", "column_break_lqmf"], "fields": [{"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"default": "DUNN-.MM.-.YY.-", "fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "DUNN-.MM.-.YY.-", "print_hide": 1}, {"fetch_from": "customer.customer_name", "fieldname": "customer_name", "fieldtype": "Data", "in_list_view": 1, "label": "Customer Name", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "label": "Date", "reqd": 1}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "dunning_type", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Dunning Type", "options": "Dunning Type"}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"default": "0", "fetch_from": "dunning_type.dunning_fee", "fetch_if_empty": 1, "fieldname": "dunning_fee", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Dunning Fee", "options": "currency", "precision": "2"}, {"fieldname": "section_break_12", "fieldtype": "Section Break"}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"fieldname": "language", "fieldtype": "Link", "label": "Print Language", "options": "Language", "print_hide": 1}, {"fieldname": "letter_head", "fieldtype": "Link", "label": "Letter Head", "options": "Letter Head", "print_hide": 1}, {"fieldname": "column_break_22", "fieldtype": "Column Break"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "<PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"fieldname": "body_text", "fieldtype": "Text Editor", "label": "Body Text"}, {"fieldname": "closing_text", "fieldtype": "Text Editor", "label": "Closing Text"}, {"fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time"}, {"default": "0", "fetch_from": "dunning_type.rate_of_interest", "fetch_if_empty": 1, "fieldname": "rate_of_interest", "fieldtype": "Float", "label": "Rate of Interest (%) Yearly"}, {"fieldname": "address_and_contact_section", "fieldtype": "Section Break"}, {"fieldname": "address_display", "fieldtype": "Text Editor", "label": "Address", "read_only": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "label": "Contact", "read_only": 1}, {"fieldname": "contact_mobile", "fieldtype": "Small Text", "label": "Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "company_address_display", "fieldtype": "Text Editor", "label": "Company Address Display", "read_only": 1}, {"fieldname": "contact_email", "fieldtype": "Data", "label": "Contact Email", "options": "Email", "read_only": 1}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer", "reqd": 1}, {"default": "0", "fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total", "options": "currency", "precision": "2", "read_only": 1}, {"allow_on_submit": 1, "default": "Unresolved", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "options": "Draft\nResolved\nUnresolved\nCancelled", "read_only": 1}, {"description": "For dunning fee and interest", "fetch_from": "dunning_type.income_account", "fieldname": "income_account", "fieldtype": "Link", "label": "Income Account", "options": "Account", "print_hide": 1}, {"fieldname": "overdue_payments", "fieldtype": "Table", "label": "Overdue Payments", "options": "Overdue Payment"}, {"fieldname": "section_break_28", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "total_interest", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Interest", "options": "currency", "precision": "2", "read_only": 1}, {"fieldname": "total_outstanding", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Outstanding", "options": "currency", "read_only": 1}, {"fieldname": "customer_address", "fieldtype": "Link", "label": "Customer Address", "options": "Address", "print_hide": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"default": "0", "fieldname": "dunning_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Dunning Amount", "options": "currency", "read_only": 1}, {"collapsible": 1, "fieldname": "accounting_details_section", "fieldtype": "Section Break", "label": "Accounting Details"}, {"fetch_from": "dunning_type.cost_center", "fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center", "print_hide": 1}, {"collapsible": 1, "fieldname": "printing_settings_section", "fieldtype": "Section Break", "label": "Printing Settings"}, {"fieldname": "section_break_32", "fieldtype": "Section Break"}, {"fieldname": "column_break_33", "fieldtype": "Column Break"}, {"fieldname": "spacer", "fieldtype": "Data", "hidden": 1, "label": "Spacer", "print_hide": 1, "read_only": 1, "report_hide": 1}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "company_address", "fieldtype": "Link", "label": "Company Address", "options": "Address", "print_hide": 1}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "column_break_11", "fieldtype": "Column Break"}, {"fieldname": "conversion_rate", "fieldtype": "Float", "label": "Conversion Rate"}, {"default": "0", "fieldname": "base_dunning_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Dunning Amount (Company Currency)", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "column_break_48", "fieldtype": "Column Break"}, {"fieldname": "address_and_contact_tab", "fieldtype": "Tab Break", "label": "Address & Contact"}, {"fieldname": "column_break_vodj", "fieldtype": "Column Break"}, {"fieldname": "section_break_xban", "fieldtype": "Section Break"}, {"fieldname": "column_break_lqmf", "fieldtype": "Column Break"}], "is_submittable": 1, "links": [], "modified": "2024-11-26 13:46:07.760867", "modified_by": "Administrator", "module": "Accounts", "name": "<PERSON><PERSON>", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "ASC", "states": [], "title_field": "customer_name", "track_changes": 1}