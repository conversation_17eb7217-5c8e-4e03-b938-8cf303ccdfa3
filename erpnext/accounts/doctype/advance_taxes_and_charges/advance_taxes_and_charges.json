{"actions": [], "creation": "2020-09-12 22:26:19.594367", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["add_deduct_tax", "charge_type", "row_id", "account_head", "col_break_1", "description", "included_in_paid_amount", "set_by_item_tax_template", "accounting_dimensions_section", "cost_center", "dimension_col_break", "section_break_8", "rate", "section_break_9", "currency", "net_amount", "tax_amount", "total", "allocated_amount", "column_break_13", "base_tax_amount", "base_net_amount", "base_total"], "fields": [{"columns": 2, "fieldname": "charge_type", "fieldtype": "Select", "in_list_view": 1, "label": "Type", "oldfieldname": "charge_type", "oldfieldtype": "Select", "options": "\nActual\nOn Paid Amount\nOn Previous Row Amount\nOn Previous Row Total", "reqd": 1}, {"depends_on": "eval:[\"On Previous Row Amount\", \"On Previous Row Total\"].indexOf(doc.charge_type)!==-1", "fieldname": "row_id", "fieldtype": "Data", "label": "Reference Row #", "oldfieldname": "row_id", "oldfieldtype": "Data"}, {"columns": 2, "fieldname": "account_head", "fieldtype": "Link", "in_list_view": 1, "label": "Account Head", "oldfieldname": "account_head", "oldfieldtype": "Link", "options": "Account", "reqd": 1, "search_index": 1}, {"fieldname": "col_break_1", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Small Text", "print_width": "300px", "reqd": 1, "width": "300px"}, {"fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"default": ":Company", "fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "oldfieldname": "cost_center_other_charges", "oldfieldtype": "Link", "options": "Cost Center"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "section_break_8", "fieldtype": "Section Break"}, {"columns": 2, "fieldname": "rate", "fieldtype": "Float", "in_list_view": 1, "label": "Tax Rate", "oldfieldname": "rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "section_break_9", "fieldtype": "Section Break"}, {"columns": 2, "fieldname": "tax_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "currency"}, {"columns": 2, "fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Total", "options": "currency", "read_only": 1}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}, {"fieldname": "base_tax_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount (Company Currency)", "oldfieldname": "tax_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "base_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total (Company Currency)", "oldfieldname": "total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "add_deduct_tax", "fieldtype": "Select", "label": "Add Or Deduct", "options": "Add\nDeduct", "reqd": 1}, {"default": "0", "fieldname": "included_in_paid_amount", "fieldtype": "Check", "label": "Considered In Paid Amount"}, {"fieldname": "allocated_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Allocated Amount", "options": "currency"}, {"fetch_from": "account_head.account_currency", "fieldname": "currency", "fieldtype": "Link", "label": "Account <PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"columns": 2, "fieldname": "net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Net Amount", "options": "currency", "read_only": 1}, {"fieldname": "base_net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount (Company Currency)", "oldfieldname": "tax_amount", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"default": "0", "fieldname": "set_by_item_tax_template", "fieldtype": "Check", "hidden": 1, "label": "Set by Item Tax Template", "print_hide": 1, "read_only": 1, "report_hide": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-11-22 19:16:22.346267", "modified_by": "Administrator", "module": "Accounts", "name": "Advance Taxes and Charges", "owner": "Administrator", "permissions": [], "sort_field": "creation", "sort_order": "ASC", "states": []}