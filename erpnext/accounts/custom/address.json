{"custom_fields": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2018-12-28 22:29:21.828090", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Address", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "tax_category", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 15, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "fax", "label": "Tax Category", "length": 0, "mandatory_depends_on": null, "modified": "2018-12-28 22:29:21.828090", "modified_by": "Administrator", "name": "Address-tax_category", "no_copy": 0, "options": "Tax Category", "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2020-10-14 17:41:40.878179", "default": "0", "depends_on": null, "description": null, "docstatus": 0, "dt": "Address", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_your_company_address", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 20, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "linked_with", "label": "Is Your Company Address", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-14 17:41:40.878179", "modified_by": "Administrator", "name": "Address-is_your_company_address", "no_copy": 0, "options": null, "owner": "Administrator", "parent": null, "parentfield": null, "parenttype": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}], "custom_perms": [], "doctype": "Address", "property_setters": [], "sync_on_migrate": 1}